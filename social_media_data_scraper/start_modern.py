#!/usr/bin/env python3
"""
现代化PyQt5界面
美观的多平台运营数据抓取与分析系统GUI
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Optional
import threading
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QTableWidget, QTableWidgetItem, QHeaderView,
    QFrame, QSplitter, QTextEdit, QProgressBar, QStatusBar,
    QDialog, QFormLayout, QLineEdit, QComboBox, QDialogButtonBox,
    QMessageBox, QGroupBox, QGridLayout, QScrollArea, QTabWidget
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap, QPainter, QLinearGradient

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager


class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text, color="#3498db", hover_color="#2980b9"):
        super().__init__(text)
        self.color = color
        self.hover_color = hover_color
        self.setup_style()
    
    def setup_style(self):
        """设置按钮样式"""
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {self.hover_color};
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {self.hover_color};
                transform: translateY(0px);
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """)


class AccountCard(QFrame):
    """账号卡片组件"""
    
    def __init__(self, account_data):
        super().__init__()
        self.account_data = account_data
        self.setup_ui()
    
    def setup_ui(self):
        """设置卡片界面"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #ecf0f1;
                border-radius: 12px;
                margin: 5px;
            }
            QFrame:hover {
                border-color: #3498db;
                box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 平台图标和名称
        header_layout = QHBoxLayout()
        
        # 平台图标（使用文字代替）
        platform_icon = QLabel("📱")
        platform_icon.setFont(QFont("Arial", 24))
        platform_icon.setAlignment(Qt.AlignCenter)
        platform_icon.setFixedSize(40, 40)
        
        # 账号信息
        info_layout = QVBoxLayout()
        
        platform_label = QLabel(self.account_data.get('platform_name', ''))
        platform_label.setFont(QFont("Arial", 14, QFont.Bold))
        platform_label.setStyleSheet("color: #2c3e50;")
        
        account_label = QLabel(f"账号: {self.account_data.get('account_id', '')}")
        account_label.setFont(QFont("Arial", 10))
        account_label.setStyleSheet("color: #7f8c8d;")
        
        info_layout.addWidget(platform_label)
        info_layout.addWidget(account_label)
        
        header_layout.addWidget(platform_icon)
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # 状态指示器
        status = self.account_data.get('status', '❌未登录')
        status_label = QLabel(status)
        status_label.setFont(QFont("Arial", 12))
        if "✅" in status:
            status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        else:
            status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        login_btn = ModernButton("登录", "#3498db", "#2980b9")
        login_btn.setFixedHeight(35)
        
        scrape_btn = ModernButton("抓取", "#27ae60", "#229954")
        scrape_btn.setFixedHeight(35)
        
        delete_btn = ModernButton("删除", "#e74c3c", "#c0392b")
        delete_btn.setFixedHeight(35)
        
        button_layout.addWidget(login_btn)
        button_layout.addWidget(scrape_btn)
        button_layout.addWidget(delete_btn)
        
        # 添加到主布局
        layout.addLayout(header_layout)
        layout.addWidget(status_label)
        layout.addLayout(button_layout)
        
        self.setFixedSize(280, 160)


class AddAccountDialog(QDialog):
    """添加账号对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.result_data = None
        self.setup_ui()
    
    def setup_ui(self):
        """设置对话框界面"""
        self.setWindowTitle("添加账号")
        self.setFixedSize(450, 750)
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
            }
            QLineEdit, QComboBox {
                padding: 8px;
                border: 2px solid #ecf0f1;
                border-radius: 6px;
                font-size: 14px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("添加新账号")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # 表单
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        self.account_id_edit = QLineEdit()
        self.account_id_edit.setPlaceholderText("请输入账号标识（如：主账号、测试账号等）")
        
        self.platform_combo = QComboBox()
        self.platform_combo.addItems(config.get_platform_names())
        
        form_layout.addRow("配置名称:", self.account_id_edit)
        form_layout.addRow("平台选择:", self.platform_combo)
        
        # 说明文本
        info_text = QTextEdit()
        info_text.setMaximumHeight(100)
        info_text.setStyleSheet("""
            QTextEdit {
                background-color: #e8f4fd;
                border: 1px solid #3498db;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        info_text.setPlainText(
            "注意：这是演示版本，当前会保存测试数据。\n"
            "实际使用需要配置真实的登录流程。"
        )
        info_text.setReadOnly(True)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton[text="OK"] {
                background-color: #3498db;
                color: white;
                border: none;
            }
            QPushButton[text="Cancel"] {
                background-color: #95a5a6;
                color: white;
                border: none;
            }
        """)
        
        button_box.accepted.connect(self.accept_dialog)
        button_box.rejected.connect(self.reject)
        
        # 添加到布局
        layout.addWidget(title)
        layout.addLayout(form_layout)
        layout.addWidget(info_text)
        layout.addStretch()
        layout.addWidget(button_box)
    
    def accept_dialog(self):
        """确认对话框"""
        account_id = self.account_id_edit.text().strip()
        platform_name = self.platform_combo.currentText()
        
        if not account_id or not platform_name:
            QMessageBox.warning(self, "警告", "请填写完整的账号信息")
            return
        
        platform_key = config.get_platform_key_by_name(platform_name)
        
        # 保存演示数据
        demo_cookies = {
            'demo_session': 'demo_value',
            'user_id': account_id,
            'platform': platform_key,
            'timestamp': str(int(time.time()))
        }
        
        if cookie_manager.save_cookies(account_id, platform_key, demo_cookies):
            self.result_data = {
                'account_id': account_id,
                'platform': platform_key,
                'platform_name': platform_name
            }
            QMessageBox.information(self, "成功", 
                                  f"账号 {account_id}@{platform_name} 添加成功！\n\n"
                                  "注意：这是演示数据，实际使用需要真实登录。")
            self.accept()
        else:
            QMessageBox.critical(self, "错误", "保存账号信息失败")


class ModernAccountManager(QMainWindow):
    """现代化账号管理主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger('ModernAccountManager')
        self.accounts_data = []
        self.setup_ui()
        self.load_accounts()
        
        # 定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_accounts)
        self.refresh_timer.start(30000)  # 30秒刷新一次
    
    def setup_ui(self):
        """设置主界面"""
        self.setWindowTitle(f"{config.app_name} v{config.app_version}")
        self.setMinimumSize(1200, 800)
        
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QStatusBar {
                background-color: #34495e;
                color: white;
                font-weight: bold;
            }
        """)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 顶部标题栏
        self.create_header(main_layout)
        
        # 工具栏
        self.create_toolbar(main_layout)
        
        # 主内容区域
        self.create_content_area(main_layout)
        
        # 状态栏
        self.create_status_bar()
        
        # 居中显示
        self.center_window()
    
    def create_header(self, parent_layout):
        """创建标题栏"""
        header = QFrame()
        header.setFixedHeight(80)
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border: none;
            }
        """)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(30, 0, 30, 0)
        
        # 应用图标和标题
        title_layout = QVBoxLayout()
        
        app_title = QLabel("多平台运营数据抓取与分析系统")
        app_title.setFont(QFont("Arial", 20, QFont.Bold))
        app_title.setStyleSheet("color: white;")
        
        app_subtitle = QLabel(f"版本 {config.app_version} - 现代化管理界面")
        app_subtitle.setFont(QFont("Arial", 12))
        app_subtitle.setStyleSheet("color: #ecf0f1;")
        
        title_layout.addWidget(app_title)
        title_layout.addWidget(app_subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        parent_layout.addWidget(header)
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setFixedHeight(70)
        toolbar.setStyleSheet("""
            QFrame {
                background-color: white;
                border-bottom: 2px solid #ecf0f1;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(30, 10, 30, 10)
        toolbar_layout.setSpacing(15)
        
        # 按钮
        self.add_btn = ModernButton("➕ 添加账号", "#27ae60", "#229954")
        self.add_btn.clicked.connect(self.add_account)
        
        self.refresh_btn = ModernButton("🔄 刷新", "#3498db", "#2980b9")
        self.refresh_btn.clicked.connect(self.load_accounts)
        
        self.status_btn = ModernButton("📊 系统状态", "#f39c12", "#e67e22")
        self.status_btn.clicked.connect(self.show_system_status)
        
        self.test_btn = ModernButton("🧪 运行测试", "#9b59b6", "#8e44ad")
        self.test_btn.clicked.connect(self.run_tests)
        
        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.status_btn)
        toolbar_layout.addWidget(self.test_btn)
        toolbar_layout.addStretch()
        
        parent_layout.addWidget(toolbar)
    
    def create_content_area(self, parent_layout):
        """创建主内容区域"""
        content_frame = QFrame()
        content_frame.setStyleSheet("QFrame { background-color: #f8f9fa; }")
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(30, 20, 30, 20)
        
        # 账号统计信息
        stats_frame = QFrame()
        stats_frame.setFixedHeight(100)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 12px;
                border: 1px solid #ecf0f1;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setContentsMargins(20, 20, 20, 20)
        
        self.total_accounts_label = QLabel("总账号: 0")
        self.total_accounts_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.total_accounts_label.setStyleSheet("color: #2c3e50;")
        
        self.active_accounts_label = QLabel("已登录: 0")
        self.active_accounts_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.active_accounts_label.setStyleSheet("color: #27ae60;")
        
        stats_layout.addWidget(self.total_accounts_label)
        stats_layout.addWidget(self.active_accounts_label)
        stats_layout.addStretch()
        
        # 账号卡片区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        self.cards_widget = QWidget()
        self.cards_layout = QGridLayout(self.cards_widget)
        self.cards_layout.setSpacing(20)
        self.cards_layout.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        
        scroll_area.setWidget(self.cards_widget)
        
        content_layout.addWidget(stats_frame)
        content_layout.addWidget(scroll_area)
        
        parent_layout.addWidget(content_frame)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.status_bar.showMessage("就绪 - 系统已启动")
        self.setStatusBar(self.status_bar)
    
    def center_window(self):
        """窗口居中"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def load_accounts(self):
        """加载账号列表"""
        try:
            self.logger.info("加载账号列表...")

            # 清空现有卡片
            self.clear_cards()

            # 获取账号数据
            accounts = cookie_manager.get_all_accounts()
            self.accounts_data = []

            active_count = 0
            row, col = 0, 0
            max_cols = 4  # 每行最多4个卡片

            for account in accounts:
                account_id = account['account_id']
                platform = account['platform']
                platform_config = config.get_platform_config(platform)
                platform_name = platform_config.get('name', platform)

                # 检查登录状态
                is_valid = cookie_manager.is_cookies_valid(account_id, platform)
                status = "✅ 已登录" if is_valid else "❌ 未登录"

                if is_valid:
                    active_count += 1

                account_data = {
                    'account_id': account_id,
                    'platform': platform,
                    'platform_name': platform_name,
                    'status': status,
                    'updated_at': account['updated_at']
                }

                self.accounts_data.append(account_data)

                # 创建账号卡片
                card = AccountCard(account_data)
                self.cards_layout.addWidget(card, row, col)

                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1

            # 更新统计信息
            self.total_accounts_label.setText(f"总账号: {len(accounts)}")
            self.active_accounts_label.setText(f"已登录: {active_count}")

            # 更新状态栏
            self.status_bar.showMessage(f"已加载 {len(accounts)} 个账号，其中 {active_count} 个已登录")

            self.logger.info(f"账号列表加载完成，共{len(accounts)}个账号")

        except Exception as e:
            self.logger.error(f"加载账号列表失败: {str(e)}")
            self.status_bar.showMessage(f"加载失败: {str(e)}")

    def clear_cards(self):
        """清空账号卡片"""
        while self.cards_layout.count():
            child = self.cards_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def add_account(self):
        """添加账号"""
        try:
            dialog = AddAccountDialog(self)
            if dialog.exec_() == QDialog.Accepted and dialog.result_data:
                self.load_accounts()
                self.status_bar.showMessage(f"账号添加成功: {dialog.result_data['account_id']}")

        except Exception as e:
            self.logger.error(f"添加账号失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"添加账号失败: {str(e)}")

    def show_system_status(self):
        """显示系统状态"""
        try:
            from system_integration import SystemIntegration
            system = SystemIntegration()
            status = system.check_system_status()

            # 创建状态对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("系统状态")
            dialog.setFixedSize(700, 500)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f8f9fa;
                }
                QTextEdit {
                    background-color: white;
                    border: 1px solid #ecf0f1;
                    border-radius: 8px;
                    padding: 15px;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                }
            """)

            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)

            # 标题
            title = QLabel("🔍 系统状态检查")
            title.setFont(QFont("Arial", 16, QFont.Bold))
            title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")

            # 状态文本
            text_widget = QTextEdit()
            text_widget.setPlainText(self.format_system_status(status))
            text_widget.setReadOnly(True)

            # 关闭按钮
            close_btn = ModernButton("关闭", "#95a5a6", "#7f8c8d")
            close_btn.clicked.connect(dialog.accept)

            layout.addWidget(title)
            layout.addWidget(text_widget)
            layout.addWidget(close_btn)

            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"检查系统状态失败: {str(e)}")

    def format_system_status(self, status):
        """格式化系统状态信息"""
        lines = []
        lines.append("系统状态检查结果")
        lines.append("=" * 50)
        lines.append("")

        for component, info in status.items():
            status_icon = "✅" if info['status'] == 'ok' else "❌" if info['status'] == 'error' else "⚠️"
            lines.append(f"{status_icon} {component.upper()}: {info['status']}")

            if info['status'] == 'error':
                lines.append(f"   错误: {info.get('error', '未知错误')}")
            elif component == 'accounts' and 'total_accounts' in info:
                lines.append(f"   总账号数: {info['total_accounts']}")
                lines.append(f"   有效账号数: {info['valid_accounts']}")

            lines.append("")

        return "\n".join(lines)

    def run_tests(self):
        """运行测试"""
        try:
            self.status_bar.showMessage("正在运行测试...")
            self.test_btn.setEnabled(False)

            def run_tests_thread():
                try:
                    from run_tests import main as run_tests_main
                    result = run_tests_main()

                    # 在主线程中显示结果
                    QApplication.instance().postEvent(
                        self,
                        TestResultEvent(result)
                    )

                except Exception as e:
                    QApplication.instance().postEvent(
                        self,
                        TestResultEvent(-1, str(e))
                    )

            threading.Thread(target=run_tests_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"启动测试失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动测试失败: {str(e)}")
            self.test_btn.setEnabled(True)

    def show_test_result(self, result, error=None):
        """显示测试结果"""
        self.test_btn.setEnabled(True)

        if error:
            QMessageBox.critical(self, "测试失败", f"运行测试失败: {error}")
            self.status_bar.showMessage("测试失败")
        elif result == 0:
            QMessageBox.information(self, "测试完成",
                                  "🎉 所有测试通过！\n\n测试报告已保存到 test_report.txt")
            self.status_bar.showMessage("测试完成 - 全部通过")
        else:
            QMessageBox.warning(self, "测试完成",
                              "⚠️ 部分测试失败\n\n请查看 test_report.txt 了解详情")
            self.status_bar.showMessage("测试完成 - 有失败")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            self.refresh_timer.stop()
            self.logger.info("应用程序关闭")
            event.accept()
        except Exception as e:
            self.logger.error(f"关闭应用程序失败: {str(e)}")
            event.accept()


class TestResultEvent:
    """测试结果事件"""
    def __init__(self, result, error=None):
        self.result = result
        self.error = error


def main():
    """主函数"""
    try:
        print(f"🚀 启动 {config.app_name} v{config.app_version}")
        print("=" * 60)
        print("✨ 使用现代化PyQt5界面")

        # 检查Python版本
        if sys.version_info < (3, 9):
            print("❌ 错误: 需要Python 3.9或更高版本")
            return 1

        print("✅ Python版本检查通过")
        print("✅ PyQt5依赖已安装")

        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName(config.app_name)
        app.setApplicationVersion(config.app_version)

        # 设置应用程序图标
        app.setWindowIcon(QIcon())  # 可以添加图标文件

        # 设置全局样式
        app.setStyleSheet("""
            QToolTip {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
            }
        """)

        # 创建主窗口
        window = ModernAccountManager()
        window.show()

        print("🎨 现代化GUI界面已启动")

        # 运行应用程序
        return app.exec_()

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
