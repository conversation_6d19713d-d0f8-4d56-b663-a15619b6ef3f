#!/usr/bin/env python3
"""
基础tkinter版本 - 解决GUI空白问题
使用最基础的tkinter组件，避免兼容性问题
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox, simpledialog
from pathlib import Path
import threading

# 设置环境变量
os.environ['TK_SILENCE_DEPRECATION'] = '1'

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager


class BasicAccountManager:
    """基础账号管理界面"""
    
    def __init__(self):
        self.logger = get_logger('BasicAccountManager')
        self.root = tk.Tk()
        self.accounts_data = []
        self.setup_ui()
        self.load_accounts()
    
    def setup_ui(self):
        """设置界面"""
        try:
            print("🔧 开始设置基础界面...")
            
            # 窗口基本设置
            self.root.title(f"{config.app_name} v{config.app_version}")
            self.root.geometry("900x700")
            self.root.configure(bg='#f0f0f0')
            
            # 居中显示
            self.root.update_idletasks()
            x = (self.root.winfo_screenwidth() // 2) - (450)
            y = (self.root.winfo_screenheight() // 2) - (350)
            self.root.geometry(f"900x700+{x}+{y}")
            
            # 标题
            title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
            title_frame.pack(fill=tk.X)
            title_frame.pack_propagate(False)
            
            title_label = tk.Label(title_frame, 
                                 text="多平台运营数据抓取与分析系统",
                                 font=("Arial", 18, "bold"),
                                 fg='white', bg='#2c3e50')
            title_label.pack(expand=True)
            
            # 按钮区域
            button_frame = tk.Frame(self.root, bg='#ecf0f1', height=50)
            button_frame.pack(fill=tk.X, padx=10, pady=5)
            button_frame.pack_propagate(False)
            
            # 按钮样式
            btn_style = {
                'font': ('Arial', 10),
                'height': 2,
                'relief': 'raised',
                'bd': 2
            }
            
            # 添加账号按钮
            self.add_btn = tk.Button(button_frame, text="添加账号", 
                                   bg='#3498db', fg='white',
                                   command=self.add_account, **btn_style)
            self.add_btn.pack(side=tk.LEFT, padx=5, pady=5)
            
            # 刷新按钮
            self.refresh_btn = tk.Button(button_frame, text="刷新", 
                                       bg='#2ecc71', fg='white',
                                       command=self.load_accounts, **btn_style)
            self.refresh_btn.pack(side=tk.LEFT, padx=5, pady=5)
            
            # 系统状态按钮
            self.status_btn = tk.Button(button_frame, text="系统状态", 
                                      bg='#f39c12', fg='white',
                                      command=self.show_system_status, **btn_style)
            self.status_btn.pack(side=tk.LEFT, padx=5, pady=5)
            
            # 测试报告按钮
            self.test_btn = tk.Button(button_frame, text="运行测试", 
                                    bg='#9b59b6', fg='white',
                                    command=self.run_tests, **btn_style)
            self.test_btn.pack(side=tk.LEFT, padx=5, pady=5)
            
            # 删除账号按钮
            self.delete_btn = tk.Button(button_frame, text="删除选中", 
                                      bg='#e74c3c', fg='white',
                                      command=self.delete_selected, **btn_style)
            self.delete_btn.pack(side=tk.LEFT, padx=5, pady=5)
            
            # 账号列表区域
            list_frame = tk.Frame(self.root, bg='#ecf0f1')
            list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
            
            # 列表标题
            header_frame = tk.Frame(list_frame, bg='#34495e', height=30)
            header_frame.pack(fill=tk.X)
            header_frame.pack_propagate(False)
            
            headers = ['账号ID', '平台', '状态', '更新时间']
            header_widths = [200, 150, 100, 200]
            
            for i, (header, width) in enumerate(zip(headers, header_widths)):
                label = tk.Label(header_frame, text=header, 
                               font=('Arial', 10, 'bold'),
                               fg='white', bg='#34495e',
                               width=width//8)
                label.pack(side=tk.LEFT, padx=2, pady=2)
            
            # 账号列表
            list_container = tk.Frame(list_frame)
            list_container.pack(fill=tk.BOTH, expand=True)
            
            # 滚动条
            scrollbar = tk.Scrollbar(list_container)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 列表框
            self.listbox = tk.Listbox(list_container, 
                                    yscrollcommand=scrollbar.set,
                                    font=('Arial', 10),
                                    selectmode=tk.SINGLE)
            self.listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.config(command=self.listbox.yview)
            
            # 状态栏
            status_frame = tk.Frame(self.root, bg='#95a5a6', height=30)
            status_frame.pack(fill=tk.X)
            status_frame.pack_propagate(False)
            
            self.status_var = tk.StringVar()
            self.status_var.set("就绪 - 系统已启动")
            status_label = tk.Label(status_frame, textvariable=self.status_var,
                                  font=('Arial', 9), bg='#95a5a6', fg='white')
            status_label.pack(side=tk.LEFT, padx=10, pady=5)
            
            print("✅ 基础界面设置完成")
            
            # 强制更新显示
            self.root.update()
            
        except Exception as e:
            print(f"❌ 界面设置失败: {e}")
            import traceback
            traceback.print_exc()
    
    def load_accounts(self):
        """加载账号列表"""
        try:
            print("📋 加载账号列表...")
            
            # 清空列表
            self.listbox.delete(0, tk.END)
            self.accounts_data = []
            
            # 获取账号数据
            accounts = cookie_manager.get_all_accounts()
            
            for account in accounts:
                account_id = account['account_id']
                platform = account['platform']
                platform_config = config.get_platform_config(platform)
                platform_name = platform_config.get('name', platform)
                
                # 检查登录状态
                is_valid = cookie_manager.is_cookies_valid(account_id, platform)
                status = "✅已登录" if is_valid else "❌未登录"
                
                # 格式化显示文本
                display_text = f"{account_id:<20} | {platform_name:<10} | {status:<8} | {account['updated_at']}"
                
                # 添加到列表
                self.listbox.insert(tk.END, display_text)
                self.accounts_data.append({
                    'account_id': account_id,
                    'platform': platform,
                    'platform_name': platform_name,
                    'status': status,
                    'updated_at': account['updated_at']
                })
            
            self.status_var.set(f"已加载 {len(accounts)} 个账号")
            print(f"✅ 账号列表加载完成，共{len(accounts)}个账号")
            
        except Exception as e:
            print(f"❌ 加载账号列表失败: {e}")
            self.status_var.set(f"加载失败: {str(e)}")
    
    def add_account(self):
        """添加账号"""
        try:
            # 获取账号ID
            account_id = simpledialog.askstring("添加账号", "请输入账号标识:")
            if not account_id:
                return
            
            # 选择平台
            platforms = config.get_platform_names()
            platform_window = tk.Toplevel(self.root)
            platform_window.title("选择平台")
            platform_window.geometry("300x200")
            platform_window.transient(self.root)
            platform_window.grab_set()
            
            selected_platform = tk.StringVar()
            
            tk.Label(platform_window, text="请选择平台:", font=('Arial', 12)).pack(pady=10)
            
            for platform in platforms:
                tk.Radiobutton(platform_window, text=platform, 
                             variable=selected_platform, value=platform,
                             font=('Arial', 10)).pack(anchor=tk.W, padx=20)
            
            def confirm_platform():
                platform_window.destroy()
            
            tk.Button(platform_window, text="确定", command=confirm_platform,
                     bg='#3498db', fg='white', font=('Arial', 10)).pack(pady=10)
            
            platform_window.wait_window()
            
            platform_name = selected_platform.get()
            if not platform_name:
                return
            
            # 获取平台键
            platform_key = config.get_platform_key_by_name(platform_name)
            
            # 保存演示数据
            demo_cookies = {
                'demo_session': 'demo_value',
                'user_id': account_id,
                'platform': platform_key,
                'timestamp': str(int(time.time()))
            }
            
            if cookie_manager.save_cookies(account_id, platform_key, demo_cookies):
                messagebox.showinfo("成功", f"账号 {account_id}@{platform_name} 添加成功！\n\n注意：这是演示数据")
                self.load_accounts()
            else:
                messagebox.showerror("错误", "保存账号信息失败")
                
        except Exception as e:
            print(f"❌ 添加账号失败: {e}")
            messagebox.showerror("错误", f"添加账号失败: {str(e)}")
    
    def delete_selected(self):
        """删除选中的账号"""
        try:
            selection = self.listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要删除的账号")
                return
            
            index = selection[0]
            account_data = self.accounts_data[index]
            
            if messagebox.askyesno("确认删除", 
                                 f"确定要删除账号 {account_data['account_id']}@{account_data['platform_name']} 吗？"):
                
                platform_key = config.get_platform_key_by_name(account_data['platform_name'])
                if cookie_manager.delete_cookies(account_data['account_id'], platform_key):
                    self.load_accounts()
                    self.status_var.set(f"账号已删除: {account_data['account_id']}")
                else:
                    messagebox.showerror("错误", "删除账号失败")
                    
        except Exception as e:
            print(f"❌ 删除账号失败: {e}")
            messagebox.showerror("错误", f"删除账号失败: {str(e)}")
    
    def show_system_status(self):
        """显示系统状态"""
        try:
            from system_integration import SystemIntegration
            system = SystemIntegration()
            status = system.check_system_status()
            
            # 创建状态窗口
            status_window = tk.Toplevel(self.root)
            status_window.title("系统状态")
            status_window.geometry("600x400")
            status_window.configure(bg='white')
            
            # 创建文本框
            text_widget = tk.Text(status_window, wrap=tk.WORD, font=('Arial', 10))
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 显示状态信息
            text_widget.insert(tk.END, "系统状态检查结果:\n")
            text_widget.insert(tk.END, "=" * 40 + "\n\n")
            
            for component, info in status.items():
                status_icon = "✅" if info['status'] == 'ok' else "❌" if info['status'] == 'error' else "⚠️"
                text_widget.insert(tk.END, f"{status_icon} {component.upper()}: {info['status']}\n")
                
                if info['status'] == 'error':
                    text_widget.insert(tk.END, f"   错误: {info.get('error', '未知错误')}\n")
                elif component == 'accounts' and 'total_accounts' in info:
                    text_widget.insert(tk.END, f"   总账号数: {info['total_accounts']}\n")
                    text_widget.insert(tk.END, f"   有效账号数: {info['valid_accounts']}\n")
                
                text_widget.insert(tk.END, "\n")
            
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("错误", f"检查系统状态失败: {str(e)}")
    
    def run_tests(self):
        """运行测试"""
        try:
            self.status_var.set("正在运行测试...")
            
            def run_tests_thread():
                try:
                    from run_tests import main as run_tests_main
                    result = run_tests_main()
                    
                    self.root.after(0, lambda: self.show_test_result(result))
                    
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("错误", f"运行测试失败: {str(e)}"))
                    self.root.after(0, lambda: self.status_var.set("测试失败"))
            
            threading.Thread(target=run_tests_thread, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动测试失败: {str(e)}")
            self.status_var.set("就绪")
    
    def show_test_result(self, result):
        """显示测试结果"""
        if result == 0:
            messagebox.showinfo("测试完成", "所有测试通过！\n测试报告已保存到 test_report.txt")
            self.status_var.set("测试完成 - 全部通过")
        else:
            messagebox.showwarning("测试完成", "部分测试失败\n请查看 test_report.txt 了解详情")
            self.status_var.set("测试完成 - 有失败")
    
    def run(self):
        """运行应用"""
        try:
            print("🚀 启动基础GUI应用...")
            self.root.mainloop()
            print("✅ GUI应用结束")
        except Exception as e:
            print(f"❌ GUI运行异常: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    try:
        import time
        
        # 设置环境变量
        os.environ['TK_SILENCE_DEPRECATION'] = '1'
        
        print(f"启动 {config.app_name} v{config.app_version}")
        print("=" * 50)
        print("🔧 使用基础tkinter版本解决GUI空白问题")
        
        # 检查Python版本
        if sys.version_info < (3, 9):
            print("❌ 错误: 需要Python 3.9或更高版本")
            return 1
        
        print("✅ Python版本检查通过")
        print("✅ 基础依赖已安装")
        
        # 创建并运行应用
        app = BasicAccountManager()
        app.run()
        
        return 0
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
