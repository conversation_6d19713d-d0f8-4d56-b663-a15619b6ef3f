"""
数据解析器测试用例
"""

import unittest
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data.data_parser import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON>er, DataParserFactory
)


class TestBaseDataParser(unittest.TestCase):
    """基础数据解析器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = BaseDataParser('test_platform')
    
    def test_parser_initialization(self):
        """测试解析器初始化"""
        self.assertEqual(self.parser.platform, 'test_platform')
        self.assertIsNotNone(self.parser.logger)
    
    def test_parse_json_response(self):
        """测试JSON响应解析"""
        # 测试有效JSON
        valid_json = '{"code": 0, "data": {"test": "value"}}'
        result = self.parser.parse_json_response(valid_json)
        self.assertEqua<PERSON>(result, {"code": 0, "data": {"test": "value"}})
        
        # 测试无效JSON
        invalid_json = '{"invalid": json}'
        result = self.parser.parse_json_response(invalid_json)
        self.assertIsNone(result)
    
    def test_parse_html_response(self):
        """测试HTML响应解析"""
        html = '<html><body><div>Test</div></body></html>'
        soup = self.parser.parse_html_response(html)
        self.assertIsNotNone(soup)
        self.assertEqual(soup.find('div').text, 'Test')
    
    def test_extract_data_not_implemented(self):
        """测试基类extract_data方法未实现"""
        with self.assertRaises(NotImplementedError):
            self.parser.extract_data({})


class TestXiaohongshuParser(unittest.TestCase):
    """小红书数据解析器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = XiaohongshuParser()
    
    def test_parser_initialization(self):
        """测试解析器初始化"""
        self.assertEqual(self.parser.platform, 'xiaohongshu')
    
    def test_extract_data_with_dict(self):
        """测试从字典提取数据"""
        raw_data = {
            'data': {
                'total_revenue': 1000,
                'successful_refunds': 50,
                'order_count': 20,
                'user_count': 100,
                'engagement_rate': 5.5,
                'conversion_rate': 20.0
            }
        }
        
        result = self.parser.extract_data(raw_data)
        
        self.assertEqual(result['platform'], 'xiaohongshu')
        self.assertIn('timestamp', result)
        self.assertIn('data', result)
        
        data = result['data']
        self.assertEqual(data['total_revenue'], 1000)
        self.assertEqual(data['successful_refunds'], 50)
        self.assertEqual(data['order_count'], 20)
        self.assertEqual(data['user_count'], 100)
        self.assertEqual(data['engagement_rate'], 5.5)
        self.assertEqual(data['conversion_rate'], 20.0)
    
    def test_extract_data_with_json_string(self):
        """测试从JSON字符串提取数据"""
        raw_data = json.dumps({
            'data': {
                'total_revenue': 2000,
                'successful_refunds': 100
            }
        })
        
        result = self.parser.extract_data(raw_data)
        
        self.assertEqual(result['platform'], 'xiaohongshu')
        self.assertEqual(result['data']['total_revenue'], 2000)
        self.assertEqual(result['data']['successful_refunds'], 100)
    
    def test_extract_data_empty(self):
        """测试提取空数据"""
        result = self.parser.extract_data({})
        self.assertEqual(result, {})
        
        result = self.parser.extract_data(None)
        self.assertEqual(result, {})


class TestDouyinParser(unittest.TestCase):
    """抖音数据解析器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = DouyinParser()
    
    def test_parser_initialization(self):
        """测试解析器初始化"""
        self.assertEqual(self.parser.platform, 'douyin')
    
    def test_extract_data_with_dict(self):
        """测试从字典提取数据"""
        raw_data = {
            'data': {
                'total_revenue': 1500,
                'successful_refunds': 75,
                'video_views': 10000,
                'likes_count': 500,
                'comments_count': 100,
                'shares_count': 50,
                'followers_count': 1000
            }
        }
        
        result = self.parser.extract_data(raw_data)
        
        self.assertEqual(result['platform'], 'douyin')
        self.assertIn('timestamp', result)
        self.assertIn('data', result)
        
        data = result['data']
        self.assertEqual(data['total_revenue'], 1500)
        self.assertEqual(data['video_views'], 10000)
        self.assertEqual(data['likes_count'], 500)
        self.assertEqual(data['followers_count'], 1000)


class TestKuaishouParser(unittest.TestCase):
    """快手数据解析器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = KuaishouParser()
    
    def test_parser_initialization(self):
        """测试解析器初始化"""
        self.assertEqual(self.parser.platform, 'kuaishou')
    
    def test_extract_data_with_dict(self):
        """测试从字典提取数据"""
        raw_data = {
            'data': {
                'total_revenue': 1200,
                'successful_refunds': 60,
                'video_plays': 8000,
                'likes_count': 400,
                'comments_count': 80,
                'shares_count': 40,
                'fans_count': 800
            }
        }
        
        result = self.parser.extract_data(raw_data)
        
        self.assertEqual(result['platform'], 'kuaishou')
        self.assertIn('timestamp', result)
        self.assertIn('data', result)
        
        data = result['data']
        self.assertEqual(data['total_revenue'], 1200)
        self.assertEqual(data['video_plays'], 8000)
        self.assertEqual(data['likes_count'], 400)
        self.assertEqual(data['fans_count'], 800)


class TestDataParserFactory(unittest.TestCase):
    """数据解析器工厂测试"""
    
    def test_create_xiaohongshu_parser(self):
        """测试创建小红书解析器"""
        parser = DataParserFactory.create_parser('xiaohongshu')
        self.assertIsInstance(parser, XiaohongshuParser)
        self.assertEqual(parser.platform, 'xiaohongshu')
    
    def test_create_douyin_parser(self):
        """测试创建抖音解析器"""
        parser = DataParserFactory.create_parser('douyin')
        self.assertIsInstance(parser, DouyinParser)
        self.assertEqual(parser.platform, 'douyin')
    
    def test_create_kuaishou_parser(self):
        """测试创建快手解析器"""
        parser = DataParserFactory.create_parser('kuaishou')
        self.assertIsInstance(parser, KuaishouParser)
        self.assertEqual(parser.platform, 'kuaishou')
    
    def test_create_unsupported_parser(self):
        """测试创建不支持的解析器"""
        parser = DataParserFactory.create_parser('unsupported')
        self.assertIsNone(parser)
    
    def test_get_supported_platforms(self):
        """测试获取支持的平台列表"""
        platforms = DataParserFactory.get_supported_platforms()
        expected_platforms = ['xiaohongshu', 'douyin', 'kuaishou']
        
        self.assertEqual(len(platforms), len(expected_platforms))
        for platform in expected_platforms:
            self.assertIn(platform, platforms)


class TestDataParserIntegration(unittest.TestCase):
    """数据解析器集成测试"""
    
    def test_all_parsers_consistency(self):
        """测试所有解析器的一致性"""
        platforms = ['xiaohongshu', 'douyin', 'kuaishou']
        
        for platform in platforms:
            parser = DataParserFactory.create_parser(platform)
            self.assertIsNotNone(parser)
            self.assertEqual(parser.platform, platform)
            
            # 测试基本方法存在
            self.assertTrue(hasattr(parser, 'extract_data'))
            self.assertTrue(hasattr(parser, 'parse_json_response'))
            self.assertTrue(hasattr(parser, 'parse_html_response'))
    
    def test_parsers_extract_data_format(self):
        """测试解析器提取数据格式一致性"""
        platforms = ['xiaohongshu', 'douyin', 'kuaishou']
        test_data = {
            'data': {
                'total_revenue': 1000,
                'successful_refunds': 50
            }
        }
        
        for platform in platforms:
            parser = DataParserFactory.create_parser(platform)
            result = parser.extract_data(test_data)
            
            # 检查返回格式
            self.assertIsInstance(result, dict)
            if result:  # 如果有数据
                self.assertIn('platform', result)
                self.assertIn('timestamp', result)
                self.assertIn('data', result)
                self.assertEqual(result['platform'], platform)
    
    def test_parsers_handle_empty_data(self):
        """测试解析器处理空数据"""
        platforms = ['xiaohongshu', 'douyin', 'kuaishou']
        
        for platform in platforms:
            parser = DataParserFactory.create_parser(platform)
            
            # 测试空字典
            result = parser.extract_data({})
            self.assertEqual(result, {})
            
            # 测试None
            result = parser.extract_data(None)
            self.assertEqual(result, {})
    
    def test_parsers_timestamp_format(self):
        """测试解析器时间戳格式"""
        platforms = ['xiaohongshu', 'douyin', 'kuaishou']
        test_data = {'data': {'test': 'value'}}
        
        for platform in platforms:
            parser = DataParserFactory.create_parser(platform)
            result = parser.extract_data(test_data)
            
            if result and 'timestamp' in result:
                # 验证时间戳格式（ISO格式）
                timestamp = result['timestamp']
                try:
                    datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except ValueError:
                    self.fail(f"Invalid timestamp format for {platform}: {timestamp}")


if __name__ == '__main__':
    unittest.main()
