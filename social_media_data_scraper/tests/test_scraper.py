"""
数据抓取模块测试用例
"""

import unittest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scraper.base_scraper import BaseScraper
from scraper.xiaohongshu import XiaohongshuScraper
from scraper.douyin import DouyinScraper
from scraper.kuaishou import KuaishouScraper


class TestBaseScraper(unittest.TestCase):
    """基础抓取器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建一个具体的抓取器实例用于测试
        self.scraper = XiaohongshuScraper()
    
    def test_scraper_initialization(self):
        """测试抓取器初始化"""
        self.assertEqual(self.scraper.platform, 'xiaohongshu')
        self.assertIsNotNone(self.scraper.platform_config)
        self.assertIsNotNone(self.scraper.data_parser)
    
    def test_get_api_endpoints(self):
        """测试获取API端点"""
        endpoints = self.scraper.get_api_endpoints()
        self.assertIsInstance(endpoints, dict)
        self.assertGreater(len(endpoints), 0)
    
    def test_build_request_headers(self):
        """测试构建请求头"""
        cookies = {'test_cookie': 'test_value'}
        headers = self.scraper.build_request_headers(cookies)
        self.assertIsInstance(headers, dict)
        self.assertIn('User-Agent', headers)
    
    def test_validate_response(self):
        """测试响应验证"""
        # 测试有效响应
        valid_response = {'code': 0, 'data': {'test': 'data'}}
        self.assertTrue(self.scraper.validate_response(valid_response))
        
        # 测试无效响应
        invalid_response = {'code': -1, 'msg': 'error'}
        self.assertFalse(self.scraper.validate_response(invalid_response))
    
    @patch('data.cookie_manager.cookie_manager.get_cookies')
    def test_get_cookies(self, mock_get_cookies):
        """测试获取cookies"""
        mock_get_cookies.return_value = {'test': 'cookie'}
        
        cookies = self.scraper.get_cookies('test_account')
        self.assertEqual(cookies, {'test': 'cookie'})
        mock_get_cookies.assert_called_once_with('test_account', 'xiaohongshu')
    
    @patch('data.cookie_manager.cookie_manager.is_cookies_valid')
    def test_is_login_valid(self, mock_is_valid):
        """测试登录验证"""
        mock_is_valid.return_value = True
        
        result = self.scraper.is_login_valid('test_account')
        self.assertTrue(result)
        mock_is_valid.assert_called_once_with('test_account', 'xiaohongshu')
    
    def test_get_scraper_info(self):
        """测试获取抓取器信息"""
        info = self.scraper.get_scraper_info()
        self.assertIsInstance(info, dict)
        self.assertEqual(info['platform'], 'xiaohongshu')
        self.assertIn('platform_name', info)
        self.assertIn('supported_data_types', info)


class TestXiaohongshuScraper(unittest.TestCase):
    """小红书抓取器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.scraper = XiaohongshuScraper()
    
    def test_get_api_endpoints(self):
        """测试小红书API端点"""
        endpoints = self.scraper.get_api_endpoints()
        expected_keys = ['user_info', 'notes_data', 'analytics_data', 'revenue_data', 'engagement_data']
        
        for key in expected_keys:
            self.assertIn(key, endpoints)
            self.assertTrue(endpoints[key].startswith('http'))
    
    def test_build_request_headers(self):
        """测试小红书请求头"""
        cookies = {'x-s': 'test_xs', 'x-t': 'test_xt'}
        headers = self.scraper.build_request_headers(cookies)
        
        self.assertIn('X-S', headers)
        self.assertIn('X-T', headers)
        self.assertEqual(headers['X-S'], 'test_xs')
        self.assertEqual(headers['X-T'], 'test_xt')
    
    def test_validate_response(self):
        """测试小红书响应验证"""
        # 测试成功响应
        success_response = {'code': 0, 'data': {'user_id': '123'}}
        self.assertTrue(self.scraper.validate_response(success_response))
        
        # 测试错误响应
        error_response = {'code': -1, 'msg': 'error message'}
        self.assertFalse(self.scraper.validate_response(error_response))
        
        # 测试需要登录响应
        login_required = {'need_login': True}
        self.assertFalse(self.scraper.validate_response(login_required))


class TestDouyinScraper(unittest.TestCase):
    """抖音抓取器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.scraper = DouyinScraper()
    
    def test_get_api_endpoints(self):
        """测试抖音API端点"""
        endpoints = self.scraper.get_api_endpoints()
        expected_keys = ['user_info', 'video_data', 'analytics_data', 'revenue_data', 'live_data']
        
        for key in expected_keys:
            self.assertIn(key, endpoints)
            self.assertTrue(endpoints[key].startswith('http'))
    
    def test_build_request_headers(self):
        """测试抖音请求头"""
        cookies = {'msToken': 'test_token', 'ttwid': 'test_ttwid'}
        headers = self.scraper.build_request_headers(cookies)
        
        self.assertIn('X-MS-Token', headers)
        self.assertIn('X-TT-Token', headers)
        self.assertEqual(headers['X-MS-Token'], 'test_token')
        self.assertEqual(headers['X-TT-Token'], 'test_ttwid')
    
    def test_validate_response(self):
        """测试抖音响应验证"""
        # 测试成功响应
        success_response = {'status_code': 0, 'data': {'user': {'uid': '123'}}}
        self.assertTrue(self.scraper.validate_response(success_response))
        
        # 测试错误响应
        error_response = {'status_code': -1, 'status_msg': 'error message'}
        self.assertFalse(self.scraper.validate_response(error_response))
        
        # 测试需要验证响应
        verify_required = {'verify_url': 'http://verify.url'}
        self.assertFalse(self.scraper.validate_response(verify_required))


class TestKuaishouScraper(unittest.TestCase):
    """快手抓取器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.scraper = KuaishouScraper()
    
    def test_get_api_endpoints(self):
        """测试快手API端点"""
        endpoints = self.scraper.get_api_endpoints()
        expected_keys = ['user_info', 'video_data', 'analytics_data', 'revenue_data', 'live_data']
        
        for key in expected_keys:
            self.assertIn(key, endpoints)
            self.assertTrue(endpoints[key].startswith('http'))
    
    def test_build_request_headers(self):
        """测试快手请求头"""
        cookies = {'kpn': 'test_kpn', 'did': 'test_did'}
        headers = self.scraper.build_request_headers(cookies)
        
        self.assertIn('X-KPN', headers)
        self.assertIn('X-DID', headers)
        self.assertEqual(headers['X-KPN'], 'test_kpn')
        self.assertEqual(headers['X-DID'], 'test_did')
    
    def test_validate_response(self):
        """测试快手响应验证"""
        # 测试成功响应
        success_response = {'result': 1, 'data': {'user_id': '123'}}
        self.assertTrue(self.scraper.validate_response(success_response))
        
        # 测试错误响应
        error_response = {'result': 0, 'error_msg': 'error message'}
        self.assertFalse(self.scraper.validate_response(error_response))
        
        # 测试需要登录响应
        login_required = {'need_login': True}
        self.assertFalse(self.scraper.validate_response(login_required))


class TestScraperIntegration(unittest.TestCase):
    """抓取器集成测试"""
    
    def test_all_scrapers_creation(self):
        """测试所有抓取器的创建"""
        scrapers = [
            XiaohongshuScraper(),
            DouyinScraper(),
            KuaishouScraper()
        ]
        
        for scraper in scrapers:
            self.assertIsInstance(scraper, BaseScraper)
            self.assertIsNotNone(scraper.platform)
            self.assertIsNotNone(scraper.platform_config)
    
    def test_scrapers_consistency(self):
        """测试抓取器一致性"""
        scrapers = [
            XiaohongshuScraper(),
            DouyinScraper(),
            KuaishouScraper()
        ]
        
        for scraper in scrapers:
            # 检查必要方法存在
            self.assertTrue(hasattr(scraper, 'get_api_endpoints'))
            self.assertTrue(hasattr(scraper, 'build_request_headers'))
            self.assertTrue(hasattr(scraper, 'validate_response'))
            self.assertTrue(hasattr(scraper, 'scrape_data'))
            
            # 检查方法返回类型
            endpoints = scraper.get_api_endpoints()
            self.assertIsInstance(endpoints, dict)
            
            headers = scraper.build_request_headers({})
            self.assertIsInstance(headers, dict)


if __name__ == '__main__':
    unittest.main()
