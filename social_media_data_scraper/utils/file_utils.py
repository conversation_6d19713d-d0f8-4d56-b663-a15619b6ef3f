"""
文件操作工具类
提供文件和目录操作的便捷方法
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from app.logger import get_logger


class FileUtils:
    """文件操作工具类"""
    
    def __init__(self):
        self.logger = get_logger('FileUtils')
    
    def ensure_directory(self, path: Path) -> bool:
        """确保目录存在"""
        try:
            path.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"创建目录失败 {path}: {str(e)}")
            return False
    
    def read_json(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """读取JSON文件"""
        try:
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"读取JSON文件失败 {file_path}: {str(e)}")
            return None
    
    def write_json(self, file_path: Path, data: Dict[str, Any]) -> bool:
        """写入JSON文件"""
        try:
            # 确保目录存在
            self.ensure_directory(file_path.parent)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"写入JSON文件失败 {file_path}: {str(e)}")
            return False
    
    def read_text(self, file_path: Path, encoding: str = 'utf-8') -> Optional[str]:
        """读取文本文件"""
        try:
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"读取文本文件失败 {file_path}: {str(e)}")
            return None
    
    def write_text(self, file_path: Path, content: str, encoding: str = 'utf-8') -> bool:
        """写入文本文件"""
        try:
            # 确保目录存在
            self.ensure_directory(file_path.parent)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            self.logger.error(f"写入文本文件失败 {file_path}: {str(e)}")
            return False
    
    def copy_file(self, src: Path, dst: Path) -> bool:
        """复制文件"""
        try:
            # 确保目标目录存在
            self.ensure_directory(dst.parent)
            
            shutil.copy2(src, dst)
            return True
        except Exception as e:
            self.logger.error(f"复制文件失败 {src} -> {dst}: {str(e)}")
            return False
    
    def move_file(self, src: Path, dst: Path) -> bool:
        """移动文件"""
        try:
            # 确保目标目录存在
            self.ensure_directory(dst.parent)
            
            shutil.move(str(src), str(dst))
            return True
        except Exception as e:
            self.logger.error(f"移动文件失败 {src} -> {dst}: {str(e)}")
            return False
    
    def delete_file(self, file_path: Path) -> bool:
        """删除文件"""
        try:
            if file_path.exists():
                file_path.unlink()
            return True
        except Exception as e:
            self.logger.error(f"删除文件失败 {file_path}: {str(e)}")
            return False
    
    def get_file_size(self, file_path: Path) -> int:
        """获取文件大小（字节）"""
        try:
            return file_path.stat().st_size if file_path.exists() else 0
        except Exception as e:
            self.logger.error(f"获取文件大小失败 {file_path}: {str(e)}")
            return 0
    
    def get_file_modified_time(self, file_path: Path) -> Optional[datetime]:
        """获取文件修改时间"""
        try:
            if not file_path.exists():
                return None
            
            timestamp = file_path.stat().st_mtime
            return datetime.fromtimestamp(timestamp)
        except Exception as e:
            self.logger.error(f"获取文件修改时间失败 {file_path}: {str(e)}")
            return None
    
    def generate_report_filename(self, prefix: str = "analysis_of_data") -> str:
        """生成报告文件名"""
        date_str = datetime.now().strftime("%Y%m%d")
        return f"{date_str}_{prefix}.xlsx"


# 全局文件工具实例
file_utils = FileUtils()
