"""
Web工具类
提供Web相关的便捷方法
"""

import time
import requests
from typing import Dict, Any, Optional
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

from app.config import config
from app.logger import get_logger


class WebUtils:
    """Web工具类"""
    
    def __init__(self):
        self.logger = get_logger('WebUtils')
        self.driver = None
    
    def create_driver(self, headless: bool = False) -> Optional[webdriver.Chrome]:
        """创建Chrome WebDriver"""
        try:
            # Chrome选项
            chrome_options = Options()
            
            if headless:
                chrome_options.add_argument('--headless')
            
            # 其他常用选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            # 禁用图片加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # 创建服务
            service = Service(ChromeDriverManager().install())
            
            # 创建驱动
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.implicitly_wait(10)
            
            self.logger.info("Chrome WebDriver创建成功")
            return driver
            
        except Exception as e:
            self.logger.error(f"创建Chrome WebDriver失败: {str(e)}")
            return None
    
    def get_driver(self, headless: bool = False) -> Optional[webdriver.Chrome]:
        """获取WebDriver实例"""
        if self.driver is None:
            self.driver = self.create_driver(headless)
        return self.driver
    
    def close_driver(self):
        """关闭WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                self.logger.info("WebDriver已关闭")
            except Exception as e:
                self.logger.error(f"关闭WebDriver失败: {str(e)}")
    
    def wait_for_element(self, driver: webdriver.Chrome, by: By, value: str, timeout: int = 10):
        """等待元素出现"""
        try:
            wait = WebDriverWait(driver, timeout)
            return wait.until(EC.presence_of_element_located((by, value)))
        except Exception as e:
            self.logger.error(f"等待元素失败 {by}={value}: {str(e)}")
            return None
    
    def get_cookies(self, driver: webdriver.Chrome) -> Dict[str, str]:
        """获取浏览器cookies"""
        try:
            cookies = driver.get_cookies()
            cookie_dict = {}
            for cookie in cookies:
                cookie_dict[cookie['name']] = cookie['value']
            return cookie_dict
        except Exception as e:
            self.logger.error(f"获取cookies失败: {str(e)}")
            return {}
    
    def set_cookies(self, driver: webdriver.Chrome, cookies: Dict[str, str]):
        """设置浏览器cookies"""
        try:
            for name, value in cookies.items():
                driver.add_cookie({'name': name, 'value': value})
            self.logger.info("设置cookies成功")
        except Exception as e:
            self.logger.error(f"设置cookies失败: {str(e)}")
    
    def make_request(self, url: str, method: str = 'GET', headers: Dict[str, str] = None, 
                    data: Dict[str, Any] = None, cookies: Dict[str, str] = None,
                    timeout: int = None) -> Optional[requests.Response]:
        """发送HTTP请求"""
        try:
            timeout = timeout or config.request_timeout
            
            # 默认请求头
            default_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
            }
            
            if headers:
                default_headers.update(headers)
            
            # 发送请求
            if method.upper() == 'GET':
                response = requests.get(url, headers=default_headers, cookies=cookies, timeout=timeout)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=default_headers, json=data, cookies=cookies, timeout=timeout)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response
            
        except Exception as e:
            self.logger.error(f"HTTP请求失败 {method} {url}: {str(e)}")
            return None
    
    def retry_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """带重试的HTTP请求"""
        for attempt in range(config.retry_times):
            try:
                response = self.make_request(url, method, **kwargs)
                if response:
                    return response
            except Exception as e:
                self.logger.warning(f"请求失败，第{attempt + 1}次尝试: {str(e)}")
                
                if attempt < config.retry_times - 1:
                    time.sleep(config.retry_delay)
        
        self.logger.error(f"请求最终失败，已重试{config.retry_times}次: {url}")
        return None


# 全局Web工具实例
web_utils = WebUtils()
