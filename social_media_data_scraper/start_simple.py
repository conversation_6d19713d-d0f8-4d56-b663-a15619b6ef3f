#!/usr/bin/env python3
"""
简化启动脚本
使用tkinter替代PyQt5，提供基础的GUI功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pathlib import Path
import threading
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager


class SimpleAccountManager:
    """简化的账号管理界面"""
    
    def __init__(self):
        self.logger = get_logger('SimpleAccountManager')
        self.root = tk.Tk()
        self.setup_ui()
        self.load_accounts()
    
    def setup_ui(self):
        """设置界面"""
        self.root.title(f"{config.app_name} v{config.app_version}")
        self.root.geometry("800x600")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="多平台运营数据抓取与分析系统", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 添加账号按钮
        self.add_btn = ttk.Button(button_frame, text="添加账号", command=self.add_account)
        self.add_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(button_frame, text="刷新", command=self.load_accounts)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 系统状态按钮
        self.status_btn = ttk.Button(button_frame, text="系统状态", command=self.show_system_status)
        self.status_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 生成报告按钮
        self.report_btn = ttk.Button(button_frame, text="生成测试报告", command=self.generate_test_report)
        self.report_btn.pack(side=tk.LEFT)
        
        # 账号列表
        columns = ('账号ID', '平台', '状态', '更新时间')
        self.tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        self.tree.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar.grid(row=2, column=3, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="删除账号", command=self.delete_account)
        self.tree.bind("<Button-2>", self.show_context_menu)  # macOS右键
        self.tree.bind("<Control-Button-1>", self.show_context_menu)  # macOS Ctrl+点击
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=3, column=0, columnspan=3, sticky=tk.W)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
    
    def load_accounts(self):
        """加载账号列表"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 获取账号列表
            accounts = cookie_manager.get_all_accounts()
            
            for account in accounts:
                account_id = account['account_id']
                platform = account['platform']
                platform_config = config.get_platform_config(platform)
                platform_name = platform_config.get('name', platform)
                
                # 检查登录状态
                is_valid = cookie_manager.is_cookies_valid(account_id, platform)
                status = "✅ 已登录" if is_valid else "❌ 未登录"
                
                # 插入数据
                self.tree.insert('', tk.END, values=(
                    account_id, platform_name, status, account['updated_at']
                ))
            
            self.status_var.set(f"已加载 {len(accounts)} 个账号")
            
        except Exception as e:
            self.logger.error(f"加载账号列表失败: {str(e)}")
            messagebox.showerror("错误", f"加载账号列表失败: {str(e)}")
    
    def add_account(self):
        """添加账号"""
        dialog = AddAccountDialog(self.root)
        if dialog.result:
            self.load_accounts()
    
    def delete_account(self):
        """删除账号"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的账号")
            return
        
        item = self.tree.item(selection[0])
        account_id = item['values'][0]
        platform_name = item['values'][1]
        
        # 获取平台键
        platform_key = config.get_platform_key_by_name(platform_name)
        
        if messagebox.askyesno("确认删除", f"确定要删除账号 {account_id}@{platform_name} 吗？"):
            try:
                if cookie_manager.delete_cookies(account_id, platform_key):
                    self.load_accounts()
                    self.status_var.set(f"账号 {account_id}@{platform_name} 已删除")
                else:
                    messagebox.showerror("错误", "删除账号失败")
            except Exception as e:
                messagebox.showerror("错误", f"删除账号失败: {str(e)}")
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def show_system_status(self):
        """显示系统状态"""
        try:
            from system_integration import SystemIntegration
            system = SystemIntegration()
            status = system.check_system_status()
            
            # 创建状态窗口
            status_window = tk.Toplevel(self.root)
            status_window.title("系统状态")
            status_window.geometry("600x400")
            
            # 创建文本框
            text_widget = tk.Text(status_window, wrap=tk.WORD, padx=10, pady=10)
            text_widget.pack(fill=tk.BOTH, expand=True)
            
            # 显示状态信息
            text_widget.insert(tk.END, "系统状态检查结果:\n")
            text_widget.insert(tk.END, "=" * 40 + "\n\n")
            
            for component, info in status.items():
                status_icon = "✅" if info['status'] == 'ok' else "❌" if info['status'] == 'error' else "⚠️"
                text_widget.insert(tk.END, f"{status_icon} {component.upper()}: {info['status']}\n")
                
                if info['status'] == 'error':
                    text_widget.insert(tk.END, f"   错误: {info.get('error', '未知错误')}\n")
                elif component == 'accounts' and 'total_accounts' in info:
                    text_widget.insert(tk.END, f"   总账号数: {info['total_accounts']}\n")
                    text_widget.insert(tk.END, f"   有效账号数: {info['valid_accounts']}\n")
                
                text_widget.insert(tk.END, "\n")
            
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("错误", f"检查系统状态失败: {str(e)}")
    
    def generate_test_report(self):
        """生成测试报告"""
        try:
            self.status_var.set("正在生成测试报告...")
            
            # 在后台线程中运行测试
            def run_tests():
                try:
                    from run_tests import main as run_tests_main
                    result = run_tests_main()
                    
                    # 在主线程中显示结果
                    self.root.after(0, lambda: self.show_test_result(result))
                    
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("错误", f"运行测试失败: {str(e)}"))
                    self.root.after(0, lambda: self.status_var.set("测试失败"))
            
            threading.Thread(target=run_tests, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动测试失败: {str(e)}")
            self.status_var.set("就绪")
    
    def show_test_result(self, result):
        """显示测试结果"""
        if result == 0:
            messagebox.showinfo("测试完成", "所有测试通过！测试报告已保存到 test_report.txt")
            self.status_var.set("测试完成 - 全部通过")
        else:
            messagebox.showwarning("测试完成", "部分测试失败，请查看 test_report.txt 了解详情")
            self.status_var.set("测试完成 - 有失败")
    
    def run(self):
        """运行应用"""
        self.root.mainloop()


class AddAccountDialog:
    """添加账号对话框"""
    
    def __init__(self, parent):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("添加账号")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        self.dialog.wait_window()
    
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置名称
        ttk.Label(main_frame, text="配置名称:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.account_id_var = tk.StringVar()
        account_id_entry = ttk.Entry(main_frame, textvariable=self.account_id_var, width=30)
        account_id_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 平台选择
        ttk.Label(main_frame, text="平台选择:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.platform_var = tk.StringVar()
        platform_combo = ttk.Combobox(main_frame, textvariable=self.platform_var, 
                                     values=config.get_platform_names(), state="readonly", width=27)
        platform_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 说明文本
        info_text = tk.Text(main_frame, height=8, width=50, wrap=tk.WORD)
        info_text.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        
        info_content = """注意：这是一个演示版本，实际的登录功能需要：

1. 安装完整的依赖包（包括selenium等）
2. 配置Chrome浏览器和WebDriver
3. 实现具体的登录流程

当前版本主要用于：
- 测试系统架构
- 验证数据处理逻辑
- 生成测试报告

如需完整功能，请安装所有依赖包。"""
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.RIGHT)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
    
    def ok_clicked(self):
        """确定按钮点击"""
        account_id = self.account_id_var.get().strip()
        platform_name = self.platform_var.get()
        
        if not account_id or not platform_name:
            messagebox.showwarning("警告", "请填写完整的账号信息")
            return
        
        try:
            # 获取平台键
            platform_key = config.get_platform_key_by_name(platform_name)
            
            # 保存一个示例cookie（实际应用中这里会是真实的登录流程）
            demo_cookies = {
                'demo_session': 'demo_value',
                'user_id': account_id,
                'platform': platform_key
            }
            
            if cookie_manager.save_cookies(account_id, platform_key, demo_cookies):
                self.result = {
                    'account_id': account_id,
                    'platform': platform_key,
                    'platform_name': platform_name
                }
                messagebox.showinfo("成功", f"账号 {account_id}@{platform_name} 添加成功！\n\n注意：这是演示数据，实际使用需要真实登录。")
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "保存账号信息失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"添加账号失败: {str(e)}")
    
    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()


def main():
    """主函数"""
    try:
        print(f"启动 {config.app_name} v{config.app_version}")
        print("=" * 50)
        
        # 检查Python版本
        if sys.version_info < (3, 9):
            print("❌ 错误: 需要Python 3.9或更高版本")
            return 1
        
        print("✅ Python版本检查通过")
        print("✅ 基础依赖已安装")
        print("🚀 启动简化版GUI界面...")
        
        # 创建并运行应用
        app = SimpleAccountManager()
        app.run()
        
        return 0
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
