#!/usr/bin/env python3
"""
系统集成脚本
整合所有模块，提供完整的数据抓取和分析流程
"""

import sys
import time
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager
from data.data_analyzer import data_analyzer
from scraper.xiaohongshu import XiaohongshuScraper
from scraper.douyin import DouyinScraper
from scraper.kuaishou import KuaishouScraper
from reports.excel_report import excel_report_generator


class SystemIntegration:
    """系统集成类"""
    
    def __init__(self):
        self.logger = get_logger('SystemIntegration')
        self.scrapers = {
            'xiaohongshu': XiaohongshuScraper(),
            'douyin': <PERSON><PERSON><PERSON><PERSON>craper(),
            'kuaishou': <PERSON><PERSON>hou<PERSON>craper()
        }
        self.scraped_data = []
    
    def check_system_status(self) -> Dict[str, Any]:
        """检查系统状态"""
        self.logger.info("检查系统状态...")
        
        status = {
            'config': self._check_config(),
            'database': self._check_database(),
            'directories': self._check_directories(),
            'scrapers': self._check_scrapers(),
            'accounts': self._check_accounts()
        }
        
        return status
    
    def _check_config(self) -> Dict[str, Any]:
        """检查配置"""
        try:
            return {
                'status': 'ok',
                'app_name': config.app_name,
                'version': config.app_version,
                'debug': config.debug,
                'platforms': len(config.platforms)
            }
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _check_database(self) -> Dict[str, Any]:
        """检查数据库"""
        try:
            # 尝试获取账号列表
            accounts = cookie_manager.get_all_accounts()
            return {
                'status': 'ok',
                'cookie_file': str(config.cookie_file),
                'accounts_count': len(accounts)
            }
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _check_directories(self) -> Dict[str, Any]:
        """检查目录"""
        try:
            directories = {
                'data_dir': config.data_dir.exists(),
                'report_dir': config.report_dir.exists(),
                'log_dir': config.log_dir.exists()
            }
            
            all_exist = all(directories.values())
            return {
                'status': 'ok' if all_exist else 'warning',
                'directories': directories
            }
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _check_scrapers(self) -> Dict[str, Any]:
        """检查抓取器"""
        try:
            scraper_status = {}
            for platform, scraper in self.scrapers.items():
                try:
                    info = scraper.get_scraper_info()
                    scraper_status[platform] = {
                        'status': 'ok',
                        'info': info
                    }
                except Exception as e:
                    scraper_status[platform] = {
                        'status': 'error',
                        'error': str(e)
                    }
            
            return {
                'status': 'ok',
                'scrapers': scraper_status
            }
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _check_accounts(self) -> Dict[str, Any]:
        """检查账号状态"""
        try:
            accounts = cookie_manager.get_all_accounts()
            account_status = []
            
            for account in accounts:
                account_id = account['account_id']
                platform = account['platform']
                
                is_valid = cookie_manager.is_cookies_valid(account_id, platform)
                account_status.append({
                    'account_id': account_id,
                    'platform': platform,
                    'valid': is_valid,
                    'updated_at': account['updated_at']
                })
            
            valid_count = sum(1 for acc in account_status if acc['valid'])
            
            return {
                'status': 'ok',
                'total_accounts': len(account_status),
                'valid_accounts': valid_count,
                'accounts': account_status
            }
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def scrape_all_data(self) -> List[Dict[str, Any]]:
        """抓取所有账号数据"""
        self.logger.info("开始抓取所有账号数据...")
        
        accounts = cookie_manager.get_all_accounts()
        scraped_data = []
        
        for account in accounts:
            account_id = account['account_id']
            platform = account['platform']
            
            # 检查登录状态
            if not cookie_manager.is_cookies_valid(account_id, platform):
                self.logger.warning(f"账号 {account_id}@{platform} 登录已过期，跳过")
                continue
            
            # 获取对应的抓取器
            scraper = self.scrapers.get(platform)
            if not scraper:
                self.logger.error(f"不支持的平台: {platform}")
                continue
            
            try:
                self.logger.info(f"抓取数据: {account_id}@{platform}")
                
                # 抓取数据
                data = scraper.scrape_comprehensive_data(account_id)
                
                if data:
                    scraped_data.append(data)
                    self.logger.info(f"数据抓取成功: {account_id}@{platform}")
                else:
                    self.logger.error(f"数据抓取失败: {account_id}@{platform}")
                
                # 添加延迟避免请求过于频繁
                time.sleep(2)
                
            except Exception as e:
                self.logger.error(f"抓取数据异常 {account_id}@{platform}: {str(e)}")
        
        self.scraped_data = scraped_data
        self.logger.info(f"数据抓取完成，共获取 {len(scraped_data)} 条数据")
        
        return scraped_data
    
    def analyze_data(self) -> Dict[str, Any]:
        """分析数据"""
        if not self.scraped_data:
            self.logger.warning("没有数据可分析")
            return {}
        
        self.logger.info("开始分析数据...")
        
        try:
            # 数据清洗
            cleaned_data = []
            for data in self.scraped_data:
                cleaned = data_analyzer.clean_data(data)
                cleaned_data.append(cleaned)
            
            # 生成分析报告
            analysis_report = data_analyzer.generate_summary_report(cleaned_data)
            
            self.logger.info("数据分析完成")
            return analysis_report
            
        except Exception as e:
            self.logger.error(f"数据分析失败: {str(e)}")
            return {}
    
    def generate_report(self) -> Path:
        """生成Excel报告"""
        if not self.scraped_data:
            self.logger.warning("没有数据可生成报告")
            return None
        
        self.logger.info("开始生成Excel报告...")
        
        try:
            # 清空之前的数据
            excel_report_generator.report_data = []
            
            # 添加数据到报告生成器
            for data in self.scraped_data:
                excel_report_generator.add_data(data)
            
            # 生成报告
            report_path = excel_report_generator.generate_report()
            
            if report_path:
                self.logger.info(f"Excel报告生成成功: {report_path}")
                return report_path
            else:
                self.logger.error("Excel报告生成失败")
                return None
                
        except Exception as e:
            self.logger.error(f"生成Excel报告异常: {str(e)}")
            return None
    
    def run_complete_workflow(self) -> Dict[str, Any]:
        """运行完整工作流程"""
        self.logger.info("开始运行完整工作流程...")
        
        workflow_result = {
            'start_time': datetime.now().isoformat(),
            'system_status': None,
            'scraped_count': 0,
            'analysis_report': None,
            'report_path': None,
            'end_time': None,
            'success': False,
            'errors': []
        }
        
        try:
            # 1. 检查系统状态
            self.logger.info("步骤1: 检查系统状态")
            workflow_result['system_status'] = self.check_system_status()
            
            # 2. 抓取数据
            self.logger.info("步骤2: 抓取数据")
            scraped_data = self.scrape_all_data()
            workflow_result['scraped_count'] = len(scraped_data)
            
            if not scraped_data:
                workflow_result['errors'].append("没有抓取到任何数据")
                return workflow_result
            
            # 3. 分析数据
            self.logger.info("步骤3: 分析数据")
            analysis_report = self.analyze_data()
            workflow_result['analysis_report'] = analysis_report
            
            # 4. 生成报告
            self.logger.info("步骤4: 生成报告")
            report_path = self.generate_report()
            if report_path:
                workflow_result['report_path'] = str(report_path)
            
            workflow_result['success'] = True
            self.logger.info("完整工作流程执行成功")
            
        except Exception as e:
            error_msg = f"工作流程执行异常: {str(e)}"
            self.logger.error(error_msg)
            workflow_result['errors'].append(error_msg)
        
        finally:
            workflow_result['end_time'] = datetime.now().isoformat()
        
        return workflow_result


def main():
    """主函数"""
    print(f"{config.app_name} - 系统集成测试")
    print("=" * 60)
    
    # 创建系统集成实例
    system = SystemIntegration()
    
    # 检查系统状态
    print("检查系统状态...")
    status = system.check_system_status()
    
    for component, info in status.items():
        status_text = "✅" if info['status'] == 'ok' else "❌" if info['status'] == 'error' else "⚠️"
        print(f"{status_text} {component}: {info['status']}")
        
        if info['status'] == 'error':
            print(f"   错误: {info.get('error', '未知错误')}")
    
    print()
    
    # 询问是否运行完整工作流程
    try:
        response = input("是否运行完整的数据抓取和分析流程？(y/N): ")
        if response.lower() in ['y', 'yes']:
            print("\n开始运行完整工作流程...")
            result = system.run_complete_workflow()
            
            print(f"\n工作流程结果:")
            print(f"开始时间: {result['start_time']}")
            print(f"结束时间: {result['end_time']}")
            print(f"抓取数据量: {result['scraped_count']}")
            print(f"成功状态: {'✅' if result['success'] else '❌'}")
            
            if result['report_path']:
                print(f"报告路径: {result['report_path']}")
            
            if result['errors']:
                print("错误信息:")
                for error in result['errors']:
                    print(f"  - {error}")
        else:
            print("跳过完整工作流程")
    
    except KeyboardInterrupt:
        print("\n用户取消操作")
    
    print("\n系统集成测试完成")


if __name__ == '__main__':
    main()
