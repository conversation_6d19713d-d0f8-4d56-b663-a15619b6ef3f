# 多平台运营数据抓取与分析系统

## 项目简介

这是一个基于Python的自动化工具，能够从小红书、抖音和快手等社交媒体平台抓取运营数据，并生成结构化的Excel报告。

## 功能特性

- 🔐 安全的账号管理和登录授权
- 📊 多平台数据抓取（小红书、抖音、快手）
- 📈 数据分析和报告生成
- 🖥️ 直观的图形用户界面
- 🔒 加密的Cookie存储
- ⏰ 定时任务支持

## 系统要求

- Python 3.9+
- Windows 10+ / macOS 10.15+ / Linux (Ubuntu 20.04+)
- Chrome浏览器（用于Selenium自动化）

## 安装说明

1. 克隆项目
```bash
git clone <repository-url>
cd social_media_data_scraper
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate  # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
复制 `.env` 文件并根据需要修改配置

## 使用方法

1. 启动应用
```bash
python app/main.py
```

2. 添加账号配置
3. 完成登录授权
4. 开始数据抓取
5. 查看生成的Excel报告

## 项目结构

```
social_media_data_scraper/
├── app/                     # 主应用
├── gui/                     # GUI界面
├── scraper/                 # 数据抓取模块
├── data/                    # 数据处理模块
├── reports/                 # 报告生成模块
├── utils/                   # 工具类
├── tests/                   # 测试用例
├── .env                     # 环境变量配置
├── requirements.txt         # 依赖包列表
└── README.md                # 项目说明文档
```

## 许可证

MIT License
