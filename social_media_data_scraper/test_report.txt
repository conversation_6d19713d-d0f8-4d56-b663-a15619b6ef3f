============================================================
测试报告
============================================================
总测试数: 22
通过: 21
失败: 0
错误: 1
跳过: 0
成功率: 95.45%

错误详情:
----------------------------------------
测试: test_scraper (unittest.loader._FailedTest)
错误: ImportError: Failed to import test module: test_scraper
Traceback (most recent call last):
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/loader.py", line 436, in _find_test_path
    module = self._get_module_from_name(name)
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/loader.py", line 377, in _get_module_from_name
    __import__(name)
  File "/Users/<USER>/Desktop/platform_analysis_tool/social_media_data_scraper/tests/test_scraper.py", line 14, in <module>
    from scraper.base_scraper import BaseScraper
  File "/Users/<USER>/Desktop/platform_analysis_tool/social_media_data_scraper/scraper/base_scraper.py", line 15, in <module>
    from utils.web_utils import web_utils
  File "/Users/<USER>/Desktop/platform_analysis_tool/social_media_data_scraper/utils/web_utils.py", line 9, in <module>
    from selenium import webdriver
ModuleNotFoundError: No module named 'selenium'




测试覆盖率报告
========================================
❌ app/config.py
❌ app/logger.py
❌ data/cookie_manager.py
✅ data/data_parser.py
❌ data/data_analyzer.py
❌ scraper/base_scraper.py
❌ scraper/xiaohongshu.py
❌ scraper/douyin.py
❌ scraper/kuaishou.py
❌ reports/excel_report.py
❌ utils/file_utils.py
❌ utils/web_utils.py

覆盖率: 1/12 (8.3%)
❌ 测试覆盖率不足 (<80%)