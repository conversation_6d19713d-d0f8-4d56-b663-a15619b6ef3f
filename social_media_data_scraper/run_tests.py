#!/usr/bin/env python3
"""
测试运行脚本
运行所有测试用例并生成测试报告
"""

import sys
import unittest
import os
from pathlib import Path
from io import StringIO

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.logger import get_logger


class TestResult:
    """测试结果统计"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.failures = []
        self.errors = []


def run_test_suite(test_dir: Path) -> TestResult:
    """运行测试套件"""
    logger = get_logger('TestRunner')
    result = TestResult()
    
    try:
        # 发现测试用例
        loader = unittest.TestLoader()
        suite = loader.discover(str(test_dir), pattern='test_*.py')
        
        # 创建测试结果收集器
        stream = StringIO()
        runner = unittest.TextTestRunner(stream=stream, verbosity=2)
        
        # 运行测试
        logger.info("开始运行测试用例...")
        test_result = runner.run(suite)
        
        # 统计结果
        result.total_tests = test_result.testsRun
        result.failed_tests = len(test_result.failures)
        result.error_tests = len(test_result.errors)
        result.skipped_tests = len(test_result.skipped) if hasattr(test_result, 'skipped') else 0
        result.passed_tests = result.total_tests - result.failed_tests - result.error_tests - result.skipped_tests
        result.failures = test_result.failures
        result.errors = test_result.errors
        
        # 输出测试结果
        output = stream.getvalue()
        print(output)
        
        return result
        
    except Exception as e:
        logger.error(f"运行测试套件失败: {str(e)}")
        return result


def generate_test_report(result: TestResult) -> str:
    """生成测试报告"""
    report = []
    report.append("=" * 60)
    report.append("测试报告")
    report.append("=" * 60)
    report.append(f"总测试数: {result.total_tests}")
    report.append(f"通过: {result.passed_tests}")
    report.append(f"失败: {result.failed_tests}")
    report.append(f"错误: {result.error_tests}")
    report.append(f"跳过: {result.skipped_tests}")
    
    if result.total_tests > 0:
        success_rate = (result.passed_tests / result.total_tests) * 100
        report.append(f"成功率: {success_rate:.2f}%")
    
    report.append("")
    
    # 失败详情
    if result.failures:
        report.append("失败详情:")
        report.append("-" * 40)
        for test, traceback in result.failures:
            report.append(f"测试: {test}")
            report.append(f"错误: {traceback}")
            report.append("")
    
    # 错误详情
    if result.errors:
        report.append("错误详情:")
        report.append("-" * 40)
        for test, traceback in result.errors:
            report.append(f"测试: {test}")
            report.append(f"错误: {traceback}")
            report.append("")
    
    return "\n".join(report)


def check_test_coverage():
    """检查测试覆盖率"""
    logger = get_logger('TestCoverage')
    
    # 主要模块列表
    main_modules = [
        'app/config.py',
        'app/logger.py',
        'data/cookie_manager.py',
        'data/data_parser.py',
        'data/data_analyzer.py',
        'scraper/base_scraper.py',
        'scraper/xiaohongshu.py',
        'scraper/douyin.py',
        'scraper/kuaishou.py',
        'reports/excel_report.py',
        'utils/file_utils.py',
        'utils/web_utils.py'
    ]
    
    # 测试文件列表
    test_files = [
        'tests/test_scraper.py',
        'tests/test_data_parser.py'
    ]
    
    logger.info("检查测试覆盖率...")
    
    coverage_report = []
    coverage_report.append("测试覆盖率报告")
    coverage_report.append("=" * 40)
    
    total_modules = len(main_modules)
    covered_modules = 0
    
    for module in main_modules:
        module_path = project_root / module
        if module_path.exists():
            # 简单检查是否有对应的测试
            module_name = module.split('/')[-1].replace('.py', '')
            has_test = any(module_name in test_file for test_file in test_files)
            
            if has_test:
                covered_modules += 1
                status = "✅"
            else:
                status = "❌"
            
            coverage_report.append(f"{status} {module}")
        else:
            coverage_report.append(f"❓ {module} (文件不存在)")
    
    coverage_percentage = (covered_modules / total_modules) * 100
    coverage_report.append("")
    coverage_report.append(f"覆盖率: {covered_modules}/{total_modules} ({coverage_percentage:.1f}%)")
    
    if coverage_percentage >= 80:
        coverage_report.append("✅ 测试覆盖率达标 (≥80%)")
    else:
        coverage_report.append("❌ 测试覆盖率不足 (<80%)")
    
    return "\n".join(coverage_report)


def main():
    """主函数"""
    logger = get_logger('TestRunner')
    
    print(f"多平台运营数据抓取与分析系统 - 测试运行器")
    print("=" * 60)
    
    # 检查测试目录
    test_dir = project_root / 'tests'
    if not test_dir.exists():
        logger.error("测试目录不存在")
        return 1
    
    # 运行测试
    result = run_test_suite(test_dir)
    
    # 生成报告
    test_report = generate_test_report(result)
    print(test_report)
    
    # 检查覆盖率
    coverage_report = check_test_coverage()
    print(coverage_report)
    
    # 保存报告到文件
    try:
        report_file = project_root / 'test_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(test_report)
            f.write("\n\n")
            f.write(coverage_report)
        
        logger.info(f"测试报告已保存到: {report_file}")
        
    except Exception as e:
        logger.error(f"保存测试报告失败: {str(e)}")
    
    # 返回退出码
    if result.failed_tests > 0 or result.error_tests > 0:
        return 1
    else:
        return 0


if __name__ == '__main__':
    sys.exit(main())
