"""
基础抓取类
定义数据抓取的通用接口和方法
"""

import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager
from data.data_parser import DataParserFactory
from utils.web_utils import web_utils


class BaseScraper(ABC):
    """基础抓取器抽象类"""
    
    def __init__(self, platform: str):
        self.platform = platform
        self.logger = get_logger(f'Scraper-{platform}')
        self.platform_config = config.get_platform_config(platform)
        self.data_parser = DataParserFactory.create_parser(platform)
        
        # 请求配置
        self.timeout = config.request_timeout
        self.retry_times = config.retry_times
        self.retry_delay = config.retry_delay
    
    @abstractmethod
    def get_api_endpoints(self) -> Dict[str, str]:
        """获取API端点（子类需要实现）"""
        pass
    
    @abstractmethod
    def build_request_headers(self, cookies: Dict[str, str]) -> Dict[str, str]:
        """构建请求头（子类需要实现）"""
        pass
    
    @abstractmethod
    def validate_response(self, response_data: Any) -> bool:
        """验证响应数据（子类需要实现）"""
        pass
    
    def get_cookies(self, account_id: str) -> Optional[Dict[str, str]]:
        """获取账号的cookies"""
        return cookie_manager.get_cookies(account_id, self.platform)
    
    def is_login_valid(self, account_id: str) -> bool:
        """检查登录是否有效"""
        cookies = self.get_cookies(account_id)
        if not cookies:
            return False
        
        # 可以添加更多的登录验证逻辑
        return True
    
    def make_api_request(self, url: str, account_id: str, method: str = 'GET', 
                        data: Dict[str, Any] = None) -> Optional[Any]:
        """发送API请求"""
        try:
            # 获取cookies
            cookies = self.get_cookies(account_id)
            if not cookies:
                self.logger.error(f"未找到账号cookies: {account_id}")
                return None
            
            # 构建请求头
            headers = self.build_request_headers(cookies)
            
            # 发送请求
            response = web_utils.retry_request(
                url=url,
                method=method,
                headers=headers,
                data=data,
                cookies=cookies
            )
            
            if not response:
                return None
            
            # 检查响应状态
            if response.status_code != 200:
                self.logger.error(f"API请求失败，状态码: {response.status_code}")
                return None
            
            # 解析响应
            try:
                response_data = response.json()
            except:
                response_data = response.text
            
            # 验证响应
            if not self.validate_response(response_data):
                self.logger.error("响应数据验证失败")
                return None
            
            return response_data
            
        except Exception as e:
            self.logger.error(f"API请求异常: {str(e)}")
            return None
    
    def scrape_data(self, account_id: str, data_types: List[str] = None) -> Dict[str, Any]:
        """抓取数据"""
        try:
            self.logger.info(f"开始抓取数据: {account_id}@{self.platform}")
            
            # 检查登录状态
            if not self.is_login_valid(account_id):
                self.logger.error(f"账号登录无效: {account_id}")
                return {}
            
            # 获取API端点
            endpoints = self.get_api_endpoints()
            
            # 抓取各类数据
            scraped_data = {}
            data_types = data_types or list(endpoints.keys())
            
            for data_type in data_types:
                if data_type not in endpoints:
                    self.logger.warning(f"不支持的数据类型: {data_type}")
                    continue
                
                self.logger.info(f"抓取数据类型: {data_type}")
                
                # 发送请求
                url = endpoints[data_type]
                response_data = self.make_api_request(url, account_id)
                
                if response_data:
                    scraped_data[data_type] = response_data
                    self.logger.info(f"数据抓取成功: {data_type}")
                else:
                    self.logger.error(f"数据抓取失败: {data_type}")
                
                # 添加延迟避免请求过于频繁
                time.sleep(1)
            
            # 解析数据
            if scraped_data and self.data_parser:
                parsed_data = self.data_parser.extract_data(scraped_data)
                self.logger.info(f"数据抓取完成: {account_id}@{self.platform}")
                return parsed_data
            
            return {}
            
        except Exception as e:
            self.logger.error(f"数据抓取异常: {str(e)}")
            return {}
    
    def test_connection(self, account_id: str) -> bool:
        """测试连接"""
        try:
            self.logger.info(f"测试连接: {account_id}@{self.platform}")
            
            # 检查cookies
            cookies = self.get_cookies(account_id)
            if not cookies:
                return False
            
            # 尝试访问基础页面
            base_url = self.platform_config.get('base_url', '')
            if not base_url:
                return False
            
            headers = self.build_request_headers(cookies)
            response = web_utils.make_request(base_url, headers=headers, cookies=cookies)
            
            if response and response.status_code == 200:
                self.logger.info(f"连接测试成功: {account_id}@{self.platform}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"连接测试失败: {str(e)}")
            return False
    
    def get_scraper_info(self) -> Dict[str, Any]:
        """获取抓取器信息"""
        return {
            'platform': self.platform,
            'platform_name': self.platform_config.get('name', ''),
            'base_url': self.platform_config.get('base_url', ''),
            'supported_data_types': list(self.get_api_endpoints().keys()),
            'timeout': self.timeout,
            'retry_times': self.retry_times
        }
