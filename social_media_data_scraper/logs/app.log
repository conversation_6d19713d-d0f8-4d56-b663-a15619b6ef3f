2025-07-08 17:18:23 | INFO     | data.cookie_manager:_init_database:67 - <PERSON><PERSON>数据库初始化成功
2025-07-08 17:22:21 | INFO     | data.cookie_manager:_init_database:67 - <PERSON><PERSON>数据库初始化成功
2025-07-08 17:24:32 | INFO     | run_tests:run_test_suite:48 - 开始运行测试用例...
2025-07-08 17:24:32 | ERROR    | data.data_parser:parse_json_response:26 - JSON解析失败: Expecting value: line 1 column 13 (char 12)
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:80 - 小红书数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:127 - 抖音数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:174 - 快手数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:80 - 小红书数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:127 - 抖音数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:174 - 快手数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:127 - 抖音数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:174 - 快手数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:80 - 小红书数据提取成功
2025-07-08 17:24:32 | INFO     | data.data_parser:extract_data:80 - 小红书数据提取成功
2025-07-08 17:24:32 | INFO     | run_tests:check_test_coverage:136 - 检查测试覆盖率...
2025-07-08 17:24:32 | INFO     | run_tests:main:206 - 测试报告已保存到: /Users/<USER>/Desktop/platform_analysis_tool/social_media_data_scraper/test_report.txt
2025-07-08 17:34:08 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 17:36:58 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 17:43:46 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:11:54 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:11:55 | INFO     | __main__:load_accounts:492 - 加载账号列表...
2025-07-08 18:11:55 | INFO     | __main__:load_accounts:544 - 账号列表加载完成，共0个账号
2025-07-08 18:12:20 | INFO     | __main__:load_accounts:492 - 加载账号列表...
2025-07-08 18:12:20 | INFO     | __main__:load_accounts:544 - 账号列表加载完成，共0个账号
2025-07-08 18:12:25 | INFO     | __main__:load_accounts:492 - 加载账号列表...
2025-07-08 18:12:25 | INFO     | __main__:load_accounts:544 - 账号列表加载完成，共0个账号
2025-07-08 18:12:32 | INFO     | __main__:closeEvent:691 - 应用程序关闭
2025-07-08 18:15:11 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:16:03 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:18:07 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:18:07 | INFO     | app.main:run:62 - 启动应用程序...
2025-07-08 18:18:07 | INFO     | app.main:setup_app:51 - 应用程序初始化完成: 多平台运营数据抓取与分析系统 v1.0.0
2025-07-08 18:18:07 | INFO     | gui.account_manager:setup_ui:375 - 账号管理界面初始化完成
2025-07-08 18:18:07 | INFO     | gui.account_manager:load_accounts:380 - 加载账号列表...
2025-07-08 18:18:08 | INFO     | app.main:create_main_window:57 - 主窗口创建完成
2025-07-08 18:23:37 | INFO     | gui.account_manager:closeEvent:629 - 应用程序关闭
2025-07-08 18:23:37 | INFO     | app.main:run:68 - 应用程序退出，退出码: 0
2025-07-08 18:23:42 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:23:42 | INFO     | app.main:run:62 - 启动应用程序...
2025-07-08 18:23:42 | INFO     | app.main:setup_app:51 - 应用程序初始化完成: 多平台运营数据抓取与分析系统 v1.0.0
2025-07-08 18:23:42 | INFO     | gui.account_manager:setup_ui:375 - 账号管理界面初始化完成
2025-07-08 18:23:42 | INFO     | gui.account_manager:load_accounts:380 - 加载账号列表...
2025-07-08 18:23:42 | INFO     | app.main:create_main_window:57 - 主窗口创建完成
2025-07-08 18:23:54 | INFO     | gui.account_manager:closeEvent:629 - 应用程序关闭
2025-07-08 18:23:54 | INFO     | app.main:run:68 - 应用程序退出，退出码: 0
2025-07-08 18:24:06 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:24:06 | INFO     | app.main:run:62 - 启动应用程序...
2025-07-08 18:24:07 | INFO     | app.main:setup_app:51 - 应用程序初始化完成: 多平台运营数据抓取与分析系统 v1.0.0
2025-07-08 18:24:07 | INFO     | gui.account_manager:setup_ui:375 - 账号管理界面初始化完成
2025-07-08 18:24:07 | INFO     | gui.account_manager:load_accounts:380 - 加载账号列表...
2025-07-08 18:24:07 | INFO     | app.main:create_main_window:57 - 主窗口创建完成
2025-07-08 18:24:37 | INFO     | gui.login_form:on_start_login_clicked:336 - 开始登录流程: 千帆@小红书
2025-07-08 18:24:47 | ERROR    | utils.web_utils:create_driver:62 - 创建Chrome WebDriver失败: [Errno 8] Exec format error: '/Users/<USER>/.wdm/drivers/chromedriver/mac64/138.0.7204.92/chromedriver-mac-arm64/THIRD_PARTY_NOTICES.chromedriver'
2025-07-08 18:24:47 | ERROR    | gui.login_form:on_login_failed:399 - 登录失败: 无法启动浏览器
2025-07-08 18:38:36 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:38:36 | INFO     | start_modern:load_accounts:551 - 加载账号列表...
2025-07-08 18:38:36 | INFO     | start_modern:load_accounts:608 - 账号列表加载完成，共0个账号
2025-07-08 18:38:39 | INFO     | gui.account_manager:closeEvent:629 - 应用程序关闭
2025-07-08 18:38:39 | INFO     | app.main:run:68 - 应用程序退出，退出码: 0
2025-07-08 18:39:06 | INFO     | start_modern:load_accounts:551 - 加载账号列表...
2025-07-08 18:39:06 | INFO     | start_modern:load_accounts:608 - 账号列表加载完成，共0个账号
2025-07-08 18:39:07 | INFO     | start_modern:closeEvent:755 - 应用程序关闭
2025-07-08 18:39:19 | INFO     | data.cookie_manager:_init_database:67 - Cookie数据库初始化成功
2025-07-08 18:39:19 | INFO     | start_modern:load_accounts:551 - 加载账号列表...
2025-07-08 18:39:19 | INFO     | start_modern:load_accounts:608 - 账号列表加载完成，共0个账号
2025-07-08 18:39:29 | INFO     | start_modern:closeEvent:755 - 应用程序关闭
