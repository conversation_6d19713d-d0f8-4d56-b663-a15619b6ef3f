"""
登录表单对话框
提供账号配置和登录授权功能
"""

import sys
import time
import webbrowser
from pathlib import Path
from typing import Dict, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QComboBox, QPushButton, QLabel, QTextEdit,
    QMessageBox, QProgressBar, QGroupBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager
from utils.web_utils import web_utils


class LoginMonitorThread(QThread):
    """登录监控线程"""

    progress_updated = pyqtSignal(str)  # 进度信息
    login_completed = pyqtSignal(dict)  # 登录完成，返回cookies
    login_failed = pyqtSignal(str)  # 登录失败

    def __init__(self, platform: str, url: str, account_id: str):
        super().__init__()
        self.platform = platform
        self.url = url
        self.account_id = account_id
        self.logger = get_logger('LoginMonitorThread')
        self.driver = None
        self.monitoring = True

    def run(self):
        """运行登录监控流程"""
        try:
            self.progress_updated.emit("正在启动浏览器...")

            # 创建WebDriver
            self.driver = web_utils.create_driver(headless=False)
            if not self.driver:
                self.login_failed.emit("无法启动浏览器")
                return

            self.progress_updated.emit(f"正在打开页面: {self.url}")

            # 打开指定URL
            self.driver.get(self.url)

            self.progress_updated.emit("正在监控登录状态...")

            # 开始监控登录状态
            self.monitor_login_status()

        except Exception as e:
            self.logger.error(f"登录监控异常: {str(e)}")
            self.login_failed.emit(str(e))
        finally:
            self.close_browser()

    def monitor_login_status(self):
        """监控登录状态"""
        import time
        check_count = 0
        max_checks = 300  # 最多检查5分钟

        while self.monitoring and check_count < max_checks:
            try:
                # 检查是否有登录相关的cookies
                cookies = self.driver.get_cookies()

                # 检查常见的登录标识
                login_indicators = ['session', 'token', 'auth', 'login', 'user']
                has_login_cookie = any(
                    any(indicator in cookie['name'].lower() for indicator in login_indicators)
                    for cookie in cookies
                )

                # 检查页面URL变化（通常登录后会跳转）
                current_url = self.driver.current_url
                url_changed = current_url != self.url

                # 检查页面标题变化
                page_title = self.driver.title

                if has_login_cookie or url_changed:
                    self.progress_updated.emit("检测到登录状态变化，正在验证...")

                    # 等待一下确保页面完全加载
                    time.sleep(2)

                    # 获取最新的cookies
                    final_cookies = {}
                    for cookie in self.driver.get_cookies():
                        final_cookies[cookie['name']] = cookie['value']

                    if final_cookies:
                        self.progress_updated.emit("登录成功！正在保存登录信息...")
                        self.login_completed.emit(final_cookies)
                        return

                # 检查页面是否包含登录失败的信息
                try:
                    page_source = self.driver.page_source.lower()
                    error_keywords = ['error', 'failed', 'invalid', '错误', '失败', '无效']
                    if any(keyword in page_source for keyword in error_keywords):
                        self.progress_updated.emit("检测到可能的登录错误...")
                except:
                    pass

                time.sleep(1)  # 每秒检查一次
                check_count += 1

                # 更新进度
                if check_count % 10 == 0:
                    remaining_time = (max_checks - check_count) // 60
                    self.progress_updated.emit(f"正在监控登录状态... (剩余 {remaining_time} 分钟)")

            except Exception as e:
                self.logger.error(f"监控过程中出错: {str(e)}")
                break

        # 超时或监控结束
        if check_count >= max_checks:
            self.login_failed.emit("登录监控超时，请重试")
        else:
            self.login_failed.emit("登录监控已停止")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
            except:
                pass


class PlatformConfigDialog(QDialog):
    """平台配置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("平台配置")
        self.setFixedSize(600, 400)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # 标题
        title = QLabel("平台配置管理")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)

        # 说明
        info = QLabel("在这里可以配置支持的平台及其图标")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #666;")

        # 平台列表
        platforms_info = QTextEdit()
        platforms_info.setPlainText(
            "当前支持的平台：\n\n"
            "📱 小红书 (xiaohongshu)\n"
            "🎵 抖音 (douyin)\n"
            "⚡ 快手 (kuaishou)\n\n"
            "如需添加新平台，请联系开发人员。"
        )
        platforms_info.setReadOnly(True)

        # 按钮
        button_layout = QHBoxLayout()
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)

        layout.addWidget(title)
        layout.addWidget(info)
        layout.addWidget(platforms_info)
        layout.addLayout(button_layout)


class LoginDialog(QDialog):
    """登录对话框"""
    
    def __init__(self, parent=None, account_id: str = "", platform: str = "", platform_name: str = ""):
        super().__init__(parent)
        self.account_id = account_id
        self.platform = platform
        self.platform_name = platform_name
        self.logger = get_logger('LoginDialog')
        self.login_thread = None
        self.account_info = None
        
        self.setup_ui()
        
        # 如果是编辑现有账号，填充信息
        if self.account_id and self.platform:
            self.account_id_edit.setText(self.account_id)
            self.platform_combo.setCurrentText(self.platform_name)
            self.account_id_edit.setEnabled(False)
            self.platform_combo.setEnabled(False)
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("账号配置与登录")
        self.setModal(True)
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 账号配置组
        config_group = QGroupBox("账号配置")
        config_layout = QFormLayout(config_group)
        
        # 配置名称
        self.account_id_edit = QLineEdit()
        self.account_id_edit.setPlaceholderText("请输入账号标识（如：主账号、测试账号等）")
        config_layout.addRow("配置名称:", self.account_id_edit)
        
        # 平台选择
        platform_layout = QHBoxLayout()
        self.platform_combo = QComboBox()
        platform_names = config.get_platform_names()
        self.platform_combo.addItems(platform_names)

        self.config_platform_btn = QPushButton("配置平台")
        self.config_platform_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        self.config_platform_btn.clicked.connect(self.on_config_platform_clicked)

        platform_layout.addWidget(self.platform_combo)
        platform_layout.addWidget(self.config_platform_btn)
        config_layout.addRow("平台选择:", platform_layout)
        
        # 数据路径（改为可输入）
        self.data_path_edit = QLineEdit()
        self.data_path_edit.setPlaceholderText("请输入要访问的URL地址")
        config_layout.addRow("数据路径:", self.data_path_edit)
        
        # 登录授权组
        login_group = QGroupBox("登录授权")
        login_layout = QVBoxLayout(login_group)
        
        # 说明文本
        info_label = QLabel(
            "工作流程：\n"
            "1. 填写账号配置信息和数据路径URL\n"
            "2. 点击'保存配置'按钮先保存配置\n"
            "3. 配置保存成功后，点击'开始登录'进行授权\n"
            "4. 系统将自动监控浏览器登录状态\n"
            "5. 登录成功后自动更新账号状态"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 11px; padding: 10px;")
        
        # 操作按钮
        login_btn_layout = QHBoxLayout()

        self.save_config_btn = QPushButton("保存配置")
        self.save_config_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.save_config_btn.clicked.connect(self.on_save_config_clicked)

        self.start_login_btn = QPushButton("开始登录")
        self.start_login_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.start_login_btn.clicked.connect(self.on_start_login_clicked)
        self.start_login_btn.setEnabled(False)  # 初始禁用，需要先保存配置

        login_btn_layout.addWidget(self.save_config_btn)
        login_btn_layout.addWidget(self.start_login_btn)
        login_btn_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 状态标签
        self.status_label = QLabel("请先完成账号配置")
        self.status_label.setStyleSheet("color: #666; font-size: 11px;")
        
        login_layout.addWidget(info_label)
        login_layout.addLayout(login_btn_layout)
        login_layout.addWidget(self.progress_bar)
        login_layout.addWidget(self.status_label)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.ok_btn.clicked.connect(self.accept)
        self.ok_btn.setEnabled(False)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        
        # 添加到主布局
        layout.addWidget(config_group)
        layout.addWidget(login_group)
        layout.addLayout(button_layout)
        
        # 连接信号
        self.account_id_edit.textChanged.connect(self.validate_form)
        self.platform_combo.currentTextChanged.connect(self.on_platform_changed)

        self.validate_form()
        self.update_data_path()  # 初始化数据路径
    
    def on_platform_changed(self):
        """平台选择变化时的处理"""
        self.update_data_path()
        self.validate_form()

    def update_data_path(self):
        """更新数据路径"""
        platform_name = self.platform_combo.currentText()
        if platform_name:
            platform_key = config.get_platform_key_by_name(platform_name)
            # 获取平台的默认URL
            platform_config = config.get_platform_config(platform_key)
            default_url = platform_config.get('base_url', f'https://{platform_key}.com')
            self.data_path_edit.setText(default_url)

    def on_config_platform_clicked(self):
        """配置平台按钮点击事件"""
        try:
            dialog = PlatformConfigDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # 刷新平台列表
                self.platform_combo.clear()
                self.platform_combo.addItems(config.get_platform_names())
                self.update_data_path()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"配置平台失败: {str(e)}")

    def validate_form(self):
        """验证表单"""
        account_id = self.account_id_edit.text().strip()
        platform_name = self.platform_combo.currentText()

        if account_id and platform_name:
            self.save_config_btn.setEnabled(True)
            self.status_label.setText("请先保存配置，然后再开始登录")
        else:
            self.save_config_btn.setEnabled(False)
            self.status_label.setText("请先完成账号配置")

    def on_save_config_clicked(self):
        """保存配置按钮点击事件"""
        try:
            account_id = self.account_id_edit.text().strip()
            platform_name = self.platform_combo.currentText()
            data_path = self.data_path_edit.text().strip()

            if not account_id or not platform_name:
                QMessageBox.warning(self, "警告", "请填写完整的账号信息")
                return

            platform_key = config.get_platform_key_by_name(platform_name)

            # 保存基础配置（无cookie状态）
            basic_config = {
                'account_id': account_id,
                'platform': platform_key,
                'data_path': data_path,
                'created_at': str(int(time.time())),
                'login_status': 'pending'  # 待登录状态
            }

            # 保存配置到cookie管理器
            if cookie_manager.save_cookies(account_id, platform_key, basic_config):
                self.status_label.setText("✅ 配置保存成功，现在可以开始登录")
                self.status_label.setStyleSheet("color: #28a745; font-size: 11px;")

                # 启用登录按钮，禁用保存按钮和编辑
                self.start_login_btn.setEnabled(True)
                self.save_config_btn.setEnabled(False)
                self.account_id_edit.setEnabled(False)
                self.platform_combo.setEnabled(False)

                QMessageBox.information(self, "配置保存成功",
                                      f"账号配置 {account_id}@{platform_name} 已保存！\n"
                                      f"数据路径: {data_path}\n\n"
                                      "现在可以点击'开始登录'进行授权。")
            else:
                QMessageBox.critical(self, "错误", "保存账号配置失败")

        except Exception as e:
            self.logger.error(f"保存配置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")
    


    def on_start_login_clicked(self):
        """开始登录按钮点击事件"""
        try:
            account_id = self.account_id_edit.text().strip()
            platform_name = self.platform_combo.currentText()
            data_path = self.data_path_edit.text().strip()

            if not account_id or not platform_name or not data_path:
                QMessageBox.warning(self, "警告", "请填写完整的账号信息和数据路径")
                return

            # 检查配置是否已保存
            platform_key = config.get_platform_key_by_name(platform_name)
            saved_config = cookie_manager.get_cookies(account_id, platform_key)
            if not saved_config or saved_config.get('login_status') != 'pending':
                QMessageBox.warning(self, "警告", "请先保存配置")
                return

            # 验证URL格式
            if not data_path.startswith(('http://', 'https://')):
                data_path = 'https://' + data_path
                self.data_path_edit.setText(data_path)

            # 创建登录监控线程
            self.login_thread = LoginMonitorThread(platform_key, data_path, account_id)
            self.login_thread.progress_updated.connect(self.on_login_progress_updated)
            self.login_thread.login_completed.connect(self.on_login_completed)
            self.login_thread.login_failed.connect(self.on_login_failed)

            # 启动登录流程
            self.login_thread.start()

            # 更新界面状态
            self.start_login_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            self.logger.info(f"开始登录流程: {account_id}@{platform_name}, URL: {data_path}")

        except Exception as e:
            self.logger.error(f"开始登录失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"开始登录失败: {str(e)}")



    def on_login_progress_updated(self, message: str):
        """登录进度更新"""
        self.status_label.setText(message)

    def on_login_completed(self, cookies: dict):
        """登录完成"""
        try:
            account_id = self.account_id_edit.text().strip()
            platform_name = self.platform_combo.currentText()
            platform_key = config.get_platform_key_by_name(platform_name)
            data_path = self.data_path_edit.text().strip()

            self.logger.info(f"登录完成: {account_id}@{platform_name}")

            # 获取已保存的配置
            saved_config = cookie_manager.get_cookies(account_id, platform_key)
            if saved_config:
                # 更新配置，添加登录信息
                updated_config = saved_config.copy()
                updated_config.update(cookies)
                updated_config['login_status'] = 'completed'
                updated_config['login_time'] = str(int(time.time()))

                # 保存更新后的配置
                if cookie_manager.save_cookies(account_id, platform_key, updated_config):
                    self.status_label.setText("✅ 登录成功，信息已保存")
                    self.status_label.setStyleSheet("color: #28a745; font-size: 11px;")

                    # 保存账号信息
                    self.account_info = {
                        'account_id': account_id,
                        'platform': platform_key,
                        'platform_name': platform_name,
                        'data_path': data_path
                    }

                    self.ok_btn.setEnabled(True)

                    QMessageBox.information(self, "成功", "登录成功！账号信息已保存。")
                else:
                    self.status_label.setText("❌ 保存登录信息失败")
                    self.status_label.setStyleSheet("color: #dc3545; font-size: 11px;")
                    QMessageBox.critical(self, "错误", "保存登录信息失败")
            else:
                QMessageBox.critical(self, "错误", "未找到已保存的配置，请重新保存配置")

            # 清理
            self.cleanup_login_thread()

        except Exception as e:
            self.logger.error(f"处理登录完成事件失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理登录结果失败: {str(e)}")

    def on_login_failed(self, error: str):
        """登录失败"""
        try:
            self.logger.error(f"登录失败: {error}")

            self.status_label.setText(f"❌ 登录失败: {error}")
            self.status_label.setStyleSheet("color: #dc3545; font-size: 11px;")

            QMessageBox.critical(self, "登录失败", f"登录失败: {error}")

            # 清理
            self.cleanup_login_thread()

        except Exception as e:
            self.logger.error(f"处理登录失败事件失败: {str(e)}")

    def cleanup_login_thread(self):
        """清理登录线程"""
        try:
            if self.login_thread:
                self.login_thread.close_browser()
                if self.login_thread.isRunning():
                    self.login_thread.terminate()
                    self.login_thread.wait()
                self.login_thread = None

            # 重置界面状态
            self.start_login_btn.setEnabled(True)
            self.get_cookies_btn.setEnabled(False)
            self.progress_bar.setVisible(False)

        except Exception as e:
            self.logger.error(f"清理登录线程失败: {str(e)}")

    def get_account_info(self) -> Optional[Dict]:
        """获取账号信息"""
        return self.account_info

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            self.cleanup_login_thread()
            event.accept()
        except Exception as e:
            self.logger.error(f"关闭登录对话框失败: {str(e)}")
            event.accept()

    def reject(self):
        """取消按钮点击事件"""
        self.cleanup_login_thread()
        super().reject()
