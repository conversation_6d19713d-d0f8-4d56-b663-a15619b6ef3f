"""
登录表单对话框
提供账号配置和登录授权功能
"""

import sys
import webbrowser
from pathlib import Path
from typing import Dict, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QComboBox, QPushButton, QLabel, QTextEdit,
    QMessageBox, QProgressBar, QGroupBox, QFileDialog
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager
from utils.web_utils import web_utils


class LoginThread(QThread):
    """登录线程"""
    
    progress_updated = pyqtSignal(str)  # 进度信息
    login_completed = pyqtSignal(dict)  # 登录完成，返回cookies
    login_failed = pyqtSignal(str)  # 登录失败
    
    def __init__(self, platform: str, base_url: str):
        super().__init__()
        self.platform = platform
        self.base_url = base_url
        self.logger = get_logger('LoginThread')
        self.driver = None
    
    def run(self):
        """运行登录流程"""
        try:
            self.progress_updated.emit("正在启动浏览器...")
            
            # 创建WebDriver
            self.driver = web_utils.create_driver(headless=False)
            if not self.driver:
                self.login_failed.emit("无法启动浏览器")
                return
            
            self.progress_updated.emit("正在打开登录页面...")
            
            # 打开登录页面
            self.driver.get(self.base_url)
            
            self.progress_updated.emit("请在浏览器中完成登录，然后点击'获取登录信息'按钮")
            
            # 等待用户手动登录（这里可以添加更复杂的自动化逻辑）
            # 暂时让线程保持运行状态，等待外部调用获取cookies
            
        except Exception as e:
            self.logger.error(f"登录流程异常: {str(e)}")
            self.login_failed.emit(str(e))
        finally:
            if self.driver:
                web_utils.close_driver()
    
    def get_cookies(self):
        """获取当前页面的cookies"""
        try:
            if self.driver:
                cookies = web_utils.get_cookies(self.driver)
                if cookies:
                    self.login_completed.emit(cookies)
                else:
                    self.login_failed.emit("未能获取到有效的登录信息")
            else:
                self.login_failed.emit("浏览器未启动")
                
        except Exception as e:
            self.logger.error(f"获取cookies失败: {str(e)}")
            self.login_failed.emit(f"获取登录信息失败: {str(e)}")
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
            except:
                pass


class LoginDialog(QDialog):
    """登录对话框"""
    
    def __init__(self, parent=None, account_id: str = "", platform: str = "", platform_name: str = ""):
        super().__init__(parent)
        self.account_id = account_id
        self.platform = platform
        self.platform_name = platform_name
        self.logger = get_logger('LoginDialog')
        self.login_thread = None
        self.account_info = None
        
        self.setup_ui()
        
        # 如果是编辑现有账号，填充信息
        if self.account_id and self.platform:
            self.account_id_edit.setText(self.account_id)
            self.platform_combo.setCurrentText(self.platform_name)
            self.account_id_edit.setEnabled(False)
            self.platform_combo.setEnabled(False)
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("账号配置与登录")
        self.setModal(True)
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 账号配置组
        config_group = QGroupBox("账号配置")
        config_layout = QFormLayout(config_group)
        
        # 配置名称
        self.account_id_edit = QLineEdit()
        self.account_id_edit.setPlaceholderText("请输入账号标识（如：主账号、测试账号等）")
        config_layout.addRow("配置名称:", self.account_id_edit)
        
        # 平台选择
        self.platform_combo = QComboBox()
        platform_names = config.get_platform_names()
        self.platform_combo.addItems(platform_names)
        config_layout.addRow("平台选择:", self.platform_combo)
        
        # 数据保存路径
        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setText(str(config.report_dir))
        self.path_edit.setPlaceholderText("选择数据保存路径")
        
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.on_browse_clicked)
        
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_btn)
        config_layout.addRow("保存路径:", path_layout)
        
        # 登录授权组
        login_group = QGroupBox("登录授权")
        login_layout = QVBoxLayout(login_group)
        
        # 说明文本
        info_label = QLabel(
            "点击'开始登录'按钮将打开浏览器，请在浏览器中完成登录操作，"
            "然后返回此窗口点击'获取登录信息'按钮。"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 11px; padding: 10px;")
        
        # 登录按钮
        login_btn_layout = QHBoxLayout()
        
        self.start_login_btn = QPushButton("开始登录")
        self.start_login_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.start_login_btn.clicked.connect(self.on_start_login_clicked)
        
        self.get_cookies_btn = QPushButton("获取登录信息")
        self.get_cookies_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.get_cookies_btn.clicked.connect(self.on_get_cookies_clicked)
        self.get_cookies_btn.setEnabled(False)
        
        login_btn_layout.addWidget(self.start_login_btn)
        login_btn_layout.addWidget(self.get_cookies_btn)
        login_btn_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 状态标签
        self.status_label = QLabel("请先完成账号配置")
        self.status_label.setStyleSheet("color: #666; font-size: 11px;")
        
        login_layout.addWidget(info_label)
        login_layout.addLayout(login_btn_layout)
        login_layout.addWidget(self.progress_bar)
        login_layout.addWidget(self.status_label)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.ok_btn.clicked.connect(self.accept)
        self.ok_btn.setEnabled(False)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        
        # 添加到主布局
        layout.addWidget(config_group)
        layout.addWidget(login_group)
        layout.addLayout(button_layout)
        
        # 连接信号
        self.account_id_edit.textChanged.connect(self.validate_form)
        self.platform_combo.currentTextChanged.connect(self.validate_form)
        
        self.validate_form()
    
    def validate_form(self):
        """验证表单"""
        account_id = self.account_id_edit.text().strip()
        platform_name = self.platform_combo.currentText()
        
        if account_id and platform_name:
            self.start_login_btn.setEnabled(True)
            self.status_label.setText("可以开始登录")
        else:
            self.start_login_btn.setEnabled(False)
            self.status_label.setText("请先完成账号配置")
    
    def on_browse_clicked(self):
        """浏览按钮点击事件"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择数据保存目录", str(config.report_dir)
        )
        if directory:
            self.path_edit.setText(directory)

    def on_start_login_clicked(self):
        """开始登录按钮点击事件"""
        try:
            account_id = self.account_id_edit.text().strip()
            platform_name = self.platform_combo.currentText()

            if not account_id or not platform_name:
                QMessageBox.warning(self, "警告", "请先完成账号配置")
                return

            # 获取平台配置
            platform_key = config.get_platform_key_by_name(platform_name)
            if not platform_key:
                QMessageBox.critical(self, "错误", f"不支持的平台: {platform_name}")
                return

            platform_config = config.get_platform_config(platform_key)
            base_url = platform_config.get('base_url', '')

            if not base_url:
                QMessageBox.critical(self, "错误", f"平台 {platform_name} 的配置不完整")
                return

            # 创建登录线程
            self.login_thread = LoginThread(platform_key, base_url)
            self.login_thread.progress_updated.connect(self.on_login_progress_updated)
            self.login_thread.login_completed.connect(self.on_login_completed)
            self.login_thread.login_failed.connect(self.on_login_failed)

            # 启动登录流程
            self.login_thread.start()

            # 更新界面状态
            self.start_login_btn.setEnabled(False)
            self.get_cookies_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            self.logger.info(f"开始登录流程: {account_id}@{platform_name}")

        except Exception as e:
            self.logger.error(f"开始登录失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"开始登录失败: {str(e)}")

    def on_get_cookies_clicked(self):
        """获取登录信息按钮点击事件"""
        try:
            if self.login_thread and self.login_thread.isRunning():
                self.status_label.setText("正在获取登录信息...")
                self.login_thread.get_cookies()
            else:
                QMessageBox.warning(self, "警告", "请先开始登录流程")

        except Exception as e:
            self.logger.error(f"获取登录信息失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"获取登录信息失败: {str(e)}")

    def on_login_progress_updated(self, message: str):
        """登录进度更新"""
        self.status_label.setText(message)

    def on_login_completed(self, cookies: dict):
        """登录完成"""
        try:
            account_id = self.account_id_edit.text().strip()
            platform_name = self.platform_combo.currentText()
            platform_key = config.get_platform_key_by_name(platform_name)

            self.logger.info(f"登录完成: {account_id}@{platform_name}")

            # 保存cookies
            if cookie_manager.save_cookies(account_id, platform_key, cookies):
                self.status_label.setText("✅ 登录成功，信息已保存")
                self.status_label.setStyleSheet("color: #28a745; font-size: 11px;")

                # 保存账号信息
                self.account_info = {
                    'account_id': account_id,
                    'platform': platform_key,
                    'platform_name': platform_name,
                    'save_path': self.path_edit.text().strip()
                }

                self.ok_btn.setEnabled(True)

                QMessageBox.information(self, "成功", "登录成功！账号信息已保存。")
            else:
                self.status_label.setText("❌ 保存登录信息失败")
                self.status_label.setStyleSheet("color: #dc3545; font-size: 11px;")
                QMessageBox.critical(self, "错误", "保存登录信息失败")

            # 清理
            self.cleanup_login_thread()

        except Exception as e:
            self.logger.error(f"处理登录完成事件失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理登录结果失败: {str(e)}")

    def on_login_failed(self, error: str):
        """登录失败"""
        try:
            self.logger.error(f"登录失败: {error}")

            self.status_label.setText(f"❌ 登录失败: {error}")
            self.status_label.setStyleSheet("color: #dc3545; font-size: 11px;")

            QMessageBox.critical(self, "登录失败", f"登录失败: {error}")

            # 清理
            self.cleanup_login_thread()

        except Exception as e:
            self.logger.error(f"处理登录失败事件失败: {str(e)}")

    def cleanup_login_thread(self):
        """清理登录线程"""
        try:
            if self.login_thread:
                self.login_thread.close_browser()
                if self.login_thread.isRunning():
                    self.login_thread.terminate()
                    self.login_thread.wait()
                self.login_thread = None

            # 重置界面状态
            self.start_login_btn.setEnabled(True)
            self.get_cookies_btn.setEnabled(False)
            self.progress_bar.setVisible(False)

        except Exception as e:
            self.logger.error(f"清理登录线程失败: {str(e)}")

    def get_account_info(self) -> Optional[Dict]:
        """获取账号信息"""
        return self.account_info

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            self.cleanup_login_thread()
            event.accept()
        except Exception as e:
            self.logger.error(f"关闭登录对话框失败: {str(e)}")
            event.accept()

    def reject(self):
        """取消按钮点击事件"""
        self.cleanup_login_thread()
        super().reject()
