"""
账号管理界面
主界面显示已添加的账号列表，提供账号管理功能
"""

import sys
from pathlib import Path
from typing import Dict, List, Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QScrollArea, QFrame, QMessageBox,
    QDialog, QApplication, QGroupBox, QProgressBar, QTextEdit
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QIcon, QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager
from .login_form import LoginDialog


class AccountCard(QFrame):
    """账号卡片组件"""
    
    login_requested = pyqtSignal(str, str)  # account_id, platform
    delete_requested = pyqtSignal(str, str)  # account_id, platform
    scrape_requested = pyqtSignal(str, str)  # account_id, platform
    
    def __init__(self, account_id: str, platform: str, platform_name: str):
        super().__init__()
        self.account_id = account_id
        self.platform = platform
        self.platform_name = platform_name
        self.logger = get_logger('AccountCard')
        
        self.setup_ui()
        self.update_login_status()
    
    def setup_ui(self):
        """设置界面"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: white;
                margin: 5px;
            }
            QFrame:hover {
                border-color: #007acc;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 顶部：平台图标和名称
        top_layout = QHBoxLayout()
        
        # 平台图标
        icon_label = QLabel()
        icon_path = project_root / 'gui' / 'resources' / f'{self.platform}.png'
        if icon_path.exists():
            pixmap = QPixmap(str(icon_path)).scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(pixmap)
        else:
            icon_label.setText("📱")
            icon_label.setStyleSheet("font-size: 24px;")
        
        # 平台名称和账号ID
        info_layout = QVBoxLayout()
        platform_label = QLabel(self.platform_name)
        platform_label.setFont(QFont("Arial", 15, QFont.Bold))
        platform_label.setFixedHeight(30)
        
        account_label = QLabel(f"账号: {self.account_id}")
        account_label.setStyleSheet("color: #666; font-size: 15px;")
        account_label.setFixedHeight(30)
        
        info_layout.addWidget(platform_label)
        info_layout.addWidget(account_label)
        
        top_layout.addWidget(icon_label)
        top_layout.addLayout(info_layout)
        top_layout.addStretch()
        
        # 中部：状态信息
        self.status_label = QLabel("检查登录状态...")
        self.status_label.setStyleSheet("color: #666; font-size: 10px;")
        
        # 底部：操作按钮
        button_layout = QHBoxLayout()

        self.edit_btn = QPushButton("编辑")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        self.edit_btn.clicked.connect(self.on_edit_clicked)

        self.login_btn = QPushButton("重新登录" if "✅" in self.status_label.text() else "登录授权")
        self.login_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.login_btn.clicked.connect(self.on_login_clicked)

        self.scrape_btn = QPushButton("抓取数据")
        self.scrape_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.scrape_btn.clicked.connect(self.on_scrape_clicked)

        self.delete_btn = QPushButton("删除")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.delete_btn.clicked.connect(self.on_delete_clicked)

        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.login_btn)
        button_layout.addWidget(self.scrape_btn)
        button_layout.addWidget(self.delete_btn)
        
        # 添加到主布局
        layout.addLayout(top_layout)
        layout.addWidget(self.status_label)
        layout.addLayout(button_layout)
        
        self.setFixedSize(250, 150)
    
    def update_login_status(self):
        """更新登录状态"""
        try:
            is_valid = cookie_manager.is_cookies_valid(self.account_id, self.platform)
            
            if is_valid:
                self.status_label.setText("✅ 已登录")
                self.status_label.setStyleSheet("color: #28a745; font-size: 10px;")
                self.login_btn.setText("重新登录")
                self.scrape_btn.setEnabled(True)
            else:
                self.status_label.setText("❌ 未登录")
                self.status_label.setStyleSheet("color: #dc3545; font-size: 10px;")
                self.login_btn.setText("登录授权")
                self.scrape_btn.setEnabled(False)
                
        except Exception as e:
            self.logger.error(f"更新登录状态失败: {str(e)}")
            self.status_label.setText("❓ 状态未知")
            self.status_label.setStyleSheet("color: #ffc107; font-size: 10px;")
    
    def on_edit_clicked(self):
        """编辑按钮点击事件"""
        try:
            from .login_form import LoginDialog

            # 创建编辑对话框，传入现有账号信息
            dialog = LoginDialog(self.parent(), self.account_id, self.platform, self.platform_name)
            if dialog.exec_() == QDialog.Accepted:
                # 编辑完成后更新卡片显示
                self.update_login_status()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑账号失败: {str(e)}")

    def on_login_clicked(self):
        """登录按钮点击事件"""
        # 如果是重新登录，直接打开浏览器访问数据路径
        if "✅" in self.status_label.text():
            self.open_browser_for_relogin()
        else:
            self.login_requested.emit(self.account_id, self.platform)

    def open_browser_for_relogin(self):
        """打开浏览器进行重新登录"""
        try:
            from data.cookie_manager import cookie_manager

            # 获取保存的数据路径
            cookies = cookie_manager.get_cookies(self.account_id, self.platform)
            if cookies and 'data_path' in cookies:
                data_path = cookies['data_path']

                # 打开浏览器
                import webbrowser
                webbrowser.open(data_path)

                QMessageBox.information(self, "重新登录",
                                      f"已打开浏览器访问: {data_path}\n\n"
                                      "请在浏览器中完成重新登录操作。")
            else:
                QMessageBox.warning(self, "警告", "未找到数据路径信息，请重新配置账号")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开浏览器失败: {str(e)}")

    def on_scrape_clicked(self):
        """抓取按钮点击事件"""
        self.scrape_requested.emit(self.account_id, self.platform)

    def on_delete_clicked(self):
        """删除按钮点击事件"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除账号 {self.account_id}@{self.platform_name} 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.delete_requested.emit(self.account_id, self.platform)


class DataScrapeThread(QThread):
    """数据抓取线程"""

    progress_updated = pyqtSignal(str)  # 进度信息
    scrape_completed = pyqtSignal(str, str, dict)  # account_id, platform, result
    scrape_failed = pyqtSignal(str, str, str)  # account_id, platform, error

    def __init__(self, account_id: str, platform: str):
        super().__init__()
        self.account_id = account_id
        self.platform = platform
        self.logger = get_logger('DataScrapeThread')

    def run(self):
        """运行数据抓取"""
        try:
            self.progress_updated.emit(f"开始抓取 {self.account_id}@{self.platform} 的数据...")

            # 动态导入对应的抓取器
            if self.platform == 'xiaohongshu':
                from scraper.xiaohongshu import XiaohongshuScraper
                scraper = XiaohongshuScraper()
            elif self.platform == 'douyin':
                from scraper.douyin import DouyinScraper
                scraper = DouyinScraper()
            elif self.platform == 'kuaishou':
                from scraper.kuaishou import KuaishouScraper
                scraper = KuaishouScraper()
            else:
                raise ValueError(f"不支持的平台: {self.platform}")

            # 执行数据抓取
            self.progress_updated.emit("正在抓取数据...")
            result = scraper.scrape_comprehensive_data(self.account_id)

            if result:
                self.progress_updated.emit("数据抓取完成")
                self.scrape_completed.emit(self.account_id, self.platform, result)
            else:
                self.scrape_failed.emit(self.account_id, self.platform, "数据抓取失败")

        except Exception as e:
            self.logger.error(f"数据抓取异常: {str(e)}")
            self.scrape_failed.emit(self.account_id, self.platform, str(e))


class AccountManagerWindow(QMainWindow):
    """账号管理主窗口"""

    def __init__(self):
        super().__init__()
        self.logger = get_logger('AccountManagerWindow')
        self.account_cards = {}  # 存储账号卡片
        self.scrape_threads = {}  # 存储抓取线程

        self.setup_ui()
        self.load_accounts()

        # 定时器用于更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_all_status)
        self.status_timer.start(30000)  # 30秒更新一次

    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle(f"{config.app_name} v{config.app_version}")
        self.setMinimumSize(800, 600)

        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 顶部工具栏
        toolbar_layout = QHBoxLayout()

        # 标题
        title_label = QLabel("账号管理")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))

        # 添加账号按钮
        self.add_account_btn = QPushButton("添加账号")
        self.add_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
        """)
        self.add_account_btn.clicked.connect(self.on_add_account_clicked)

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.refresh_btn.clicked.connect(self.refresh_accounts)

        # 全部抓取按钮
        self.scrape_all_btn = QPushButton("全部抓取")
        self.scrape_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: black;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.scrape_all_btn.clicked.connect(self.on_scrape_all_clicked)

        toolbar_layout.addWidget(title_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_account_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.scrape_all_btn)

        # 账号列表区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f8f9fa;
            }
        """)

        # 账号容器
        self.accounts_widget = QWidget()
        self.accounts_layout = QGridLayout(self.accounts_widget)
        self.accounts_layout.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.scroll_area.setWidget(self.accounts_widget)

        # 状态栏
        self.status_label = QLabel("就绪")
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        status_layout = QHBoxLayout()
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.progress_bar)

        # 添加到主布局
        main_layout.addLayout(toolbar_layout)
        main_layout.addWidget(self.scroll_area)
        main_layout.addLayout(status_layout)

        self.logger.info("账号管理界面初始化完成")

    def load_accounts(self):
        """加载账号列表"""
        try:
            self.logger.info("加载账号列表...")

            # 清空现有卡片
            self.clear_account_cards()

            # 获取所有账号
            accounts = cookie_manager.get_all_accounts()

            if not accounts:
                self.show_empty_message()
                return

            # 创建账号卡片
            row, col = 0, 0
            max_cols = 3  # 每行最多3个卡片

            for account in accounts:
                account_id = account['account_id']
                platform = account['platform']
                platform_config = config.get_platform_config(platform)
                platform_name = platform_config.get('name', platform)

                # 创建账号卡片
                card = AccountCard(account_id, platform, platform_name)
                card.login_requested.connect(self.on_login_requested)
                card.delete_requested.connect(self.on_delete_requested)
                card.scrape_requested.connect(self.on_scrape_requested)

                # 添加到布局
                self.accounts_layout.addWidget(card, row, col)
                self.account_cards[f"{account_id}@{platform}"] = card

                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1

            self.status_label.setText(f"已加载 {len(accounts)} 个账号")
            self.logger.info(f"账号列表加载完成，共{len(accounts)}个账号")

        except Exception as e:
            self.logger.error(f"加载账号列表失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载账号列表失败: {str(e)}")

    def clear_account_cards(self):
        """清空账号卡片"""
        for card in self.account_cards.values():
            card.deleteLater()
        self.account_cards.clear()

    def show_empty_message(self):
        """显示空状态消息"""
        empty_label = QLabel("暂无账号，请点击'添加账号'按钮添加")
        empty_label.setAlignment(Qt.AlignCenter)
        empty_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 14px;
                padding: 50px;
            }
        """)
        self.accounts_layout.addWidget(empty_label, 0, 0, 1, 3)
        if not cookie_manager.get_all_accounts():
            empty_label.hide()

    def on_add_account_clicked(self):
        """添加账号按钮点击事件"""
        try:
            dialog = LoginDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                account_info = dialog.get_account_info()
                if account_info:
                    self.logger.info(f"添加新账号: {account_info}")
                    self.refresh_accounts()

        except Exception as e:
            self.logger.error(f"添加账号失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"添加账号失败: {str(e)}")

    def refresh_accounts(self):
        """刷新账号列表"""
        self.load_accounts()

    def update_all_status(self):
        """更新所有账号状态"""
        for card in self.account_cards.values():
            card.update_login_status()

    def on_login_requested(self, account_id: str, platform: str):
        """处理登录请求"""
        try:
            self.logger.info(f"处理登录请求: {account_id}@{platform}")

            platform_config = config.get_platform_config(platform)
            platform_name = platform_config.get('name', platform)

            dialog = LoginDialog(self, account_id, platform, platform_name)
            if dialog.exec_() == QDialog.Accepted:
                # 更新对应卡片的状态
                card_key = f"{account_id}@{platform}"
                if card_key in self.account_cards:
                    self.account_cards[card_key].update_login_status()

                self.status_label.setText(f"账号 {account_id}@{platform_name} 登录成功")

        except Exception as e:
            self.logger.error(f"处理登录请求失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"登录失败: {str(e)}")

    def on_delete_requested(self, account_id: str, platform: str):
        """处理删除请求"""
        try:
            self.logger.info(f"处理删除请求: {account_id}@{platform}")

            # 删除cookies
            if cookie_manager.delete_cookies(account_id, platform):
                # 刷新界面
                self.refresh_accounts()
                self.status_label.setText(f"账号 {account_id}@{platform} 已删除")
            else:
                QMessageBox.warning(self, "警告", "删除账号失败")

        except Exception as e:
            self.logger.error(f"处理删除请求失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"删除账号失败: {str(e)}")

    def on_scrape_requested(self, account_id: str, platform: str):
        """处理抓取请求"""
        try:
            self.logger.info(f"处理抓取请求: {account_id}@{platform}")

            # 检查是否已有抓取任务在运行
            thread_key = f"{account_id}@{platform}"
            if thread_key in self.scrape_threads and self.scrape_threads[thread_key].isRunning():
                QMessageBox.information(self, "提示", "该账号的数据抓取任务正在进行中")
                return

            # 创建抓取线程
            thread = DataScrapeThread(account_id, platform)
            thread.progress_updated.connect(self.on_scrape_progress_updated)
            thread.scrape_completed.connect(self.on_scrape_completed)
            thread.scrape_failed.connect(self.on_scrape_failed)

            # 启动线程
            self.scrape_threads[thread_key] = thread
            thread.start()

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

        except Exception as e:
            self.logger.error(f"处理抓取请求失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动数据抓取失败: {str(e)}")

    def on_scrape_all_clicked(self):
        """全部抓取按钮点击事件"""
        try:
            # 获取所有已登录的账号
            logged_accounts = []
            for card in self.account_cards.values():
                if cookie_manager.is_cookies_valid(card.account_id, card.platform):
                    logged_accounts.append((card.account_id, card.platform))

            if not logged_accounts:
                QMessageBox.information(self, "提示", "没有已登录的账号可以抓取")
                return

            reply = QMessageBox.question(
                self, "确认抓取",
                f"确定要抓取所有 {len(logged_accounts)} 个已登录账号的数据吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                for account_id, platform in logged_accounts:
                    self.on_scrape_requested(account_id, platform)

        except Exception as e:
            self.logger.error(f"全部抓取失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"全部抓取失败: {str(e)}")

    def on_scrape_progress_updated(self, message: str):
        """抓取进度更新"""
        self.status_label.setText(message)

    def on_scrape_completed(self, account_id: str, platform: str, result: dict):
        """抓取完成"""
        try:
            self.logger.info(f"数据抓取完成: {account_id}@{platform}")

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 更新状态
            self.status_label.setText(f"数据抓取完成: {account_id}@{platform}")

            # 清理线程
            thread_key = f"{account_id}@{platform}"
            if thread_key in self.scrape_threads:
                del self.scrape_threads[thread_key]

            # 显示成功消息
            QMessageBox.information(
                self, "抓取完成",
                f"账号 {account_id}@{platform} 的数据抓取完成！\n"
                f"数据将保存到报告中。"
            )

        except Exception as e:
            self.logger.error(f"处理抓取完成事件失败: {str(e)}")

    def on_scrape_failed(self, account_id: str, platform: str, error: str):
        """抓取失败"""
        try:
            self.logger.error(f"数据抓取失败: {account_id}@{platform} - {error}")

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 更新状态
            self.status_label.setText(f"数据抓取失败: {account_id}@{platform}")

            # 清理线程
            thread_key = f"{account_id}@{platform}"
            if thread_key in self.scrape_threads:
                del self.scrape_threads[thread_key]

            # 显示错误消息
            QMessageBox.critical(
                self, "抓取失败",
                f"账号 {account_id}@{platform} 的数据抓取失败！\n"
                f"错误信息: {error}"
            )

        except Exception as e:
            self.logger.error(f"处理抓取失败事件失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止所有抓取线程
            for thread in self.scrape_threads.values():
                if thread.isRunning():
                    thread.terminate()
                    thread.wait()

            # 停止定时器
            self.status_timer.stop()

            self.logger.info("应用程序关闭")
            event.accept()

        except Exception as e:
            self.logger.error(f"关闭应用程序失败: {str(e)}")
            event.accept()
