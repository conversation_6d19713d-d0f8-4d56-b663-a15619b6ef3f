"""
数据分析器模块
提供数据分析和处理功能
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from app.logger import get_logger


class DataAnalyzer:
    """数据分析器"""
    
    def __init__(self):
        self.logger = get_logger('DataAnalyzer')
    
    def clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据清洗"""
        try:
            cleaned_data = data.copy()
            
            # 处理空值
            if 'data' in cleaned_data:
                for key, value in cleaned_data['data'].items():
                    if value is None:
                        cleaned_data['data'][key] = 0
                    elif isinstance(value, str) and value.strip() == '':
                        cleaned_data['data'][key] = 0
            
            self.logger.info("数据清洗完成")
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"数据清洗失败: {str(e)}")
            return data
    
    def calculate_basic_metrics(self, data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算基础指标"""
        try:
            if not data_list:
                return {}
            
            # 创建DataFrame
            df_data = []
            for item in data_list:
                if 'data' in item:
                    row = item['data'].copy()
                    row['platform'] = item.get('platform', '')
                    row['timestamp'] = item.get('timestamp', '')
                    df_data.append(row)
            
            if not df_data:
                return {}
            
            df = pd.DataFrame(df_data)
            
            # 计算基础统计指标
            metrics = {}
            
            # 数值列统计
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                metrics[f'{col}_total'] = df[col].sum()
                metrics[f'{col}_mean'] = df[col].mean()
                metrics[f'{col}_max'] = df[col].max()
                metrics[f'{col}_min'] = df[col].min()
            
            # 平台统计
            if 'platform' in df.columns:
                platform_stats = df.groupby('platform').agg({
                    col: ['sum', 'mean'] for col in numeric_columns
                }).to_dict()
                metrics['platform_stats'] = platform_stats
            
            self.logger.info("基础指标计算完成")
            return metrics
            
        except Exception as e:
            self.logger.error(f"基础指标计算失败: {str(e)}")
            return {}
    
    def calculate_growth_rate(self, current_data: Dict[str, Any], 
                            previous_data: Dict[str, Any]) -> Dict[str, float]:
        """计算增长率"""
        try:
            growth_rates = {}
            
            current_values = current_data.get('data', {})
            previous_values = previous_data.get('data', {})
            
            for key in current_values:
                if key in previous_values:
                    current_val = current_values[key]
                    previous_val = previous_values[key]
                    
                    if previous_val != 0:
                        growth_rate = ((current_val - previous_val) / previous_val) * 100
                        growth_rates[f'{key}_growth_rate'] = round(growth_rate, 2)
                    else:
                        growth_rates[f'{key}_growth_rate'] = 0.0
            
            self.logger.info("增长率计算完成")
            return growth_rates
            
        except Exception as e:
            self.logger.error(f"增长率计算失败: {str(e)}")
            return {}
    
    def calculate_conversion_metrics(self, data: Dict[str, Any]) -> Dict[str, float]:
        """计算转化指标"""
        try:
            conversion_metrics = {}
            data_values = data.get('data', {})
            
            # 转化率计算示例
            if 'order_count' in data_values and 'user_count' in data_values:
                user_count = data_values['user_count']
                order_count = data_values['order_count']
                
                if user_count > 0:
                    conversion_rate = (order_count / user_count) * 100
                    conversion_metrics['order_conversion_rate'] = round(conversion_rate, 2)
            
            # 退款率计算
            if 'successful_refunds' in data_values and 'total_revenue' in data_values:
                total_revenue = data_values['total_revenue']
                refunds = data_values['successful_refunds']
                
                if total_revenue > 0:
                    refund_rate = (refunds / total_revenue) * 100
                    conversion_metrics['refund_rate'] = round(refund_rate, 2)
            
            # 参与度计算（针对视频平台）
            if 'likes_count' in data_values and 'video_views' in data_values:
                views = data_values['video_views']
                likes = data_values['likes_count']
                
                if views > 0:
                    engagement_rate = (likes / views) * 100
                    conversion_metrics['engagement_rate'] = round(engagement_rate, 2)
            
            self.logger.info("转化指标计算完成")
            return conversion_metrics
            
        except Exception as e:
            self.logger.error(f"转化指标计算失败: {str(e)}")
            return {}
    
    def generate_summary_report(self, data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成汇总报告"""
        try:
            if not data_list:
                return {}
            
            # 基础指标
            basic_metrics = self.calculate_basic_metrics(data_list)
            
            # 平台汇总
            platform_summary = {}
            for item in data_list:
                platform = item.get('platform', 'unknown')
                if platform not in platform_summary:
                    platform_summary[platform] = {
                        'count': 0,
                        'data': {}
                    }
                
                platform_summary[platform]['count'] += 1
                
                # 累加数值数据
                item_data = item.get('data', {})
                for key, value in item_data.items():
                    if isinstance(value, (int, float)):
                        if key not in platform_summary[platform]['data']:
                            platform_summary[platform]['data'][key] = 0
                        platform_summary[platform]['data'][key] += value
            
            # 生成报告
            report = {
                'generated_at': datetime.now().isoformat(),
                'total_records': len(data_list),
                'platforms': list(platform_summary.keys()),
                'basic_metrics': basic_metrics,
                'platform_summary': platform_summary,
                'analysis_period': {
                    'start': min(item.get('timestamp', '') for item in data_list),
                    'end': max(item.get('timestamp', '') for item in data_list)
                }
            }
            
            self.logger.info("汇总报告生成完成")
            return report
            
        except Exception as e:
            self.logger.error(f"汇总报告生成失败: {str(e)}")
            return {}
    
    def filter_data_by_date(self, data_list: List[Dict[str, Any]], 
                           start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """按日期过滤数据"""
        try:
            filtered_data = []
            
            for item in data_list:
                timestamp_str = item.get('timestamp', '')
                if timestamp_str:
                    try:
                        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        if start_date <= timestamp <= end_date:
                            filtered_data.append(item)
                    except ValueError:
                        continue
            
            self.logger.info(f"日期过滤完成，筛选出{len(filtered_data)}条记录")
            return filtered_data
            
        except Exception as e:
            self.logger.error(f"日期过滤失败: {str(e)}")
            return data_list
    
    def analyze_trends(self, data_list: List[Dict[str, Any]], 
                      metric_key: str) -> Dict[str, Any]:
        """分析趋势"""
        try:
            if not data_list:
                return {}
            
            # 按时间排序
            sorted_data = sorted(data_list, key=lambda x: x.get('timestamp', ''))
            
            values = []
            timestamps = []
            
            for item in sorted_data:
                if 'data' in item and metric_key in item['data']:
                    values.append(item['data'][metric_key])
                    timestamps.append(item.get('timestamp', ''))
            
            if len(values) < 2:
                return {}
            
            # 计算趋势
            trend_analysis = {
                'metric': metric_key,
                'data_points': len(values),
                'start_value': values[0],
                'end_value': values[-1],
                'max_value': max(values),
                'min_value': min(values),
                'average_value': sum(values) / len(values),
                'total_change': values[-1] - values[0],
                'trend_direction': 'up' if values[-1] > values[0] else 'down' if values[-1] < values[0] else 'stable'
            }
            
            # 计算变化率
            if values[0] != 0:
                change_rate = ((values[-1] - values[0]) / values[0]) * 100
                trend_analysis['change_rate'] = round(change_rate, 2)
            
            self.logger.info(f"趋势分析完成: {metric_key}")
            return trend_analysis
            
        except Exception as e:
            self.logger.error(f"趋势分析失败: {str(e)}")
            return {}


# 全局数据分析器实例
data_analyzer = DataAnalyzer()
