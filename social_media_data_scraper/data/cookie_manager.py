"""
Cookie管理模块
负责安全存储和管理各个平台的登录cookie
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Optional, List
from cryptography.fernet import Fernet
import base64

from app.config import config
from app.logger import get_logger
from utils.file_utils import file_utils


class CookieManager:
    """Cookie管理器"""
    
    def __init__(self):
        self.logger = get_logger('CookieManager')
        self.db_path = config.cookie_file
        self.cipher = self._get_cipher()
        self._init_database()
    
    def _get_cipher(self) -> Fernet:
        """获取加密器"""
        # 从配置中获取密钥，如果没有则生成新的
        key = config.encryption_key.encode()
        # 确保密钥长度为32字节
        if len(key) < 32:
            key = key.ljust(32, b'0')
        elif len(key) > 32:
            key = key[:32]
        
        # 生成Fernet密钥
        fernet_key = base64.urlsafe_b64encode(key)
        return Fernet(fernet_key)
    
    def _init_database(self):
        """初始化数据库"""
        try:
            # 确保数据目录存在
            file_utils.ensure_directory(self.db_path.parent)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建cookies表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cookies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    account_id TEXT NOT NULL,
                    platform TEXT NOT NULL,
                    cookies_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    UNIQUE(account_id, platform)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            self.logger.info("Cookie数据库初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化Cookie数据库失败: {str(e)}")
    
    def _encrypt_data(self, data: str) -> str:
        """加密数据"""
        try:
            encrypted_data = self.cipher.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            self.logger.error(f"数据加密失败: {str(e)}")
            return ""
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            self.logger.error(f"数据解密失败: {str(e)}")
            return ""
    
    def save_cookies(self, account_id: str, platform: str, cookies: Dict[str, str], 
                    expires_hours: int = 24) -> bool:
        """保存cookies"""
        try:
            # 将cookies转换为JSON字符串
            cookies_json = json.dumps(cookies, ensure_ascii=False)
            
            # 加密cookies数据
            encrypted_cookies = self._encrypt_data(cookies_json)
            
            # 计算过期时间
            expires_at = datetime.now() + timedelta(hours=expires_hours)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 插入或更新cookies
            cursor.execute('''
                INSERT OR REPLACE INTO cookies 
                (account_id, platform, cookies_data, updated_at, expires_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (account_id, platform, encrypted_cookies, datetime.now(), expires_at))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"保存cookies成功: {account_id}@{platform}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存cookies失败: {str(e)}")
            return False
    
    def get_cookies(self, account_id: str, platform: str) -> Optional[Dict[str, str]]:
        """获取cookies"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT cookies_data, expires_at FROM cookies 
                WHERE account_id = ? AND platform = ?
            ''', (account_id, platform))
            
            result = cursor.fetchone()
            conn.close()
            
            if not result:
                return None
            
            encrypted_cookies, expires_at_str = result
            
            # 检查是否过期
            if expires_at_str:
                expires_at = datetime.fromisoformat(expires_at_str)
                if datetime.now() > expires_at:
                    self.logger.warning(f"Cookies已过期: {account_id}@{platform}")
                    self.delete_cookies(account_id, platform)
                    return None
            
            # 解密cookies数据
            cookies_json = self._decrypt_data(encrypted_cookies)
            if not cookies_json:
                return None
            
            cookies = json.loads(cookies_json)
            self.logger.info(f"获取cookies成功: {account_id}@{platform}")
            return cookies
            
        except Exception as e:
            self.logger.error(f"获取cookies失败: {str(e)}")
            return None
    
    def delete_cookies(self, account_id: str, platform: str) -> bool:
        """删除cookies"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM cookies WHERE account_id = ? AND platform = ?
            ''', (account_id, platform))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"删除cookies成功: {account_id}@{platform}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除cookies失败: {str(e)}")
            return False
    
    def get_all_accounts(self) -> List[Dict[str, str]]:
        """获取所有账号信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT DISTINCT account_id, platform, updated_at, expires_at 
                FROM cookies ORDER BY updated_at DESC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            accounts = []
            for result in results:
                account_id, platform, updated_at, expires_at = result
                accounts.append({
                    'account_id': account_id,
                    'platform': platform,
                    'updated_at': updated_at,
                    'expires_at': expires_at
                })
            
            return accounts
            
        except Exception as e:
            self.logger.error(f"获取账号列表失败: {str(e)}")
            return []
    
    def is_cookies_valid(self, account_id: str, platform: str) -> bool:
        """检查cookies是否有效"""
        cookies = self.get_cookies(account_id, platform)
        return cookies is not None
    
    def cleanup_expired_cookies(self) -> int:
        """清理过期的cookies"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM cookies WHERE expires_at < ?
            ''', (datetime.now(),))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            if deleted_count > 0:
                self.logger.info(f"清理过期cookies: {deleted_count}条")
            
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"清理过期cookies失败: {str(e)}")
            return 0


# 全局Cookie管理器实例
cookie_manager = CookieManager()
