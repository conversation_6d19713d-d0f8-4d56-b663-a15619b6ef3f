#!/usr/bin/env python3
"""
现代化tkinter界面
美观的多平台运营数据抓取与分析系统GUI
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from pathlib import Path
import threading
import time

# 设置环境变量
os.environ['TK_SILENCE_DEPRECATION'] = '1'

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.logger import get_logger
from data.cookie_manager import cookie_manager


class ModernStyle:
    """现代化样式配置"""
    
    # 颜色配置
    PRIMARY_COLOR = "#3498db"
    SUCCESS_COLOR = "#27ae60"
    WARNING_COLOR = "#f39c12"
    DANGER_COLOR = "#e74c3c"
    SECONDARY_COLOR = "#95a5a6"
    DARK_COLOR = "#2c3e50"
    LIGHT_COLOR = "#ecf0f1"
    WHITE_COLOR = "#ffffff"
    
    # 字体配置
    TITLE_FONT = ("Arial", 18, "bold")
    HEADER_FONT = ("Arial", 14, "bold")
    NORMAL_FONT = ("Arial", 10)
    SMALL_FONT = ("Arial", 9)


class ModernButton(tk.Button):
    """现代化按钮组件"""
    
    def __init__(self, parent, text, command=None, style="primary", **kwargs):
        # 根据样式设置颜色
        colors = {
            "primary": (ModernStyle.PRIMARY_COLOR, "#2980b9"),
            "success": (ModernStyle.SUCCESS_COLOR, "#229954"),
            "warning": (ModernStyle.WARNING_COLOR, "#e67e22"),
            "danger": (ModernStyle.DANGER_COLOR, "#c0392b"),
            "secondary": (ModernStyle.SECONDARY_COLOR, "#7f8c8d")
        }
        
        bg_color, hover_color = colors.get(style, colors["primary"])
        
        super().__init__(
            parent,
            text=text,
            command=command,
            bg=bg_color,
            fg="white",
            font=("Arial", 10, "bold"),
            relief="flat",
            bd=0,
            padx=20,
            pady=8,
            cursor="hand2",
            **kwargs
        )
        
        # 绑定悬停效果
        self.hover_color = hover_color
        self.normal_color = bg_color
        
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
    
    def on_enter(self, event):
        """鼠标进入"""
        self.config(bg=self.hover_color)
    
    def on_leave(self, event):
        """鼠标离开"""
        self.config(bg=self.normal_color)


class AccountCard(tk.Frame):
    """账号卡片组件"""
    
    def __init__(self, parent, account_data, on_delete=None):
        super().__init__(parent, bg=ModernStyle.WHITE_COLOR, relief="solid", bd=1)
        self.account_data = account_data
        self.on_delete = on_delete
        self.setup_ui()
    
    def setup_ui(self):
        """设置卡片界面"""
        # 设置卡片大小
        self.config(width=280, height=180)
        self.pack_propagate(False)
        
        # 主容器
        main_frame = tk.Frame(self, bg=ModernStyle.WHITE_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 顶部：平台信息
        header_frame = tk.Frame(main_frame, bg=ModernStyle.WHITE_COLOR)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 平台图标
        icon_label = tk.Label(header_frame, text="📱", font=("Arial", 20), bg=ModernStyle.WHITE_COLOR)
        icon_label.pack(side=tk.LEFT)
        
        # 平台信息
        info_frame = tk.Frame(header_frame, bg=ModernStyle.WHITE_COLOR)
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        platform_label = tk.Label(info_frame, 
                                 text=self.account_data.get('platform_name', ''),
                                 font=ModernStyle.HEADER_FONT,
                                 fg=ModernStyle.DARK_COLOR,
                                 bg=ModernStyle.WHITE_COLOR)
        platform_label.pack(anchor=tk.W)
        
        account_label = tk.Label(info_frame,
                               text=f"账号: {self.account_data.get('account_id', '')}",
                               font=ModernStyle.SMALL_FONT,
                               fg=ModernStyle.SECONDARY_COLOR,
                               bg=ModernStyle.WHITE_COLOR)
        account_label.pack(anchor=tk.W)
        
        data_path_label = tk.Label(info_frame,
                                 text=f"路径: {self.account_data.get('data_path', 'N/A')}",
                                 font=ModernStyle.SMALL_FONT,
                                 fg=ModernStyle.SECONDARY_COLOR,
                                 bg=ModernStyle.WHITE_COLOR)
        data_path_label.pack(anchor=tk.W)
        
        # 状态指示器
        status = self.account_data.get('status', '❌未登录')
        status_color = ModernStyle.SUCCESS_COLOR if "✅" in status else ModernStyle.DANGER_COLOR
        
        status_label = tk.Label(main_frame,
                              text=status,
                              font=("Arial", 11, "bold"),
                              fg=status_color,
                              bg=ModernStyle.WHITE_COLOR)
        status_label.pack(pady=(0, 10))
        
        # 操作按钮
        button_frame = tk.Frame(main_frame, bg=ModernStyle.WHITE_COLOR)
        button_frame.pack(fill=tk.X)
        
        login_btn = ModernButton(button_frame, "登录", style="primary")
        login_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        scrape_btn = ModernButton(button_frame, "抓取", style="success")
        scrape_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        delete_btn = ModernButton(button_frame, "删除", style="danger", 
                                command=self.delete_account)
        delete_btn.pack(side=tk.LEFT)
    
    def delete_account(self):
        """删除账号"""
        if self.on_delete:
            self.on_delete(self.account_data)


class AddAccountDialog:
    """添加账号对话框"""
    
    def __init__(self, parent):
        self.result_data = None
        self.dialog = tk.Toplevel(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置对话框界面"""
        self.dialog.title("添加账号")
        self.dialog.geometry("500x400")
        self.dialog.configure(bg=ModernStyle.LIGHT_COLOR)
        self.dialog.transient()
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (250)
        y = (self.dialog.winfo_screenheight() // 2) - (200)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        # 主容器
        main_frame = tk.Frame(self.dialog, bg=ModernStyle.WHITE_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame,
                             text="添加新账号",
                             font=ModernStyle.TITLE_FONT,
                             fg=ModernStyle.DARK_COLOR,
                             bg=ModernStyle.WHITE_COLOR)
        title_label.pack(pady=(0, 20))
        
        # 表单区域
        form_frame = tk.Frame(main_frame, bg=ModernStyle.WHITE_COLOR)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 配置名称
        tk.Label(form_frame, text="配置名称:", font=ModernStyle.NORMAL_FONT,
                fg=ModernStyle.DARK_COLOR, bg=ModernStyle.WHITE_COLOR).pack(anchor=tk.W, pady=(0, 5))
        
        self.account_id_var = tk.StringVar()
        account_entry = tk.Entry(form_frame, textvariable=self.account_id_var,
                               font=ModernStyle.NORMAL_FONT, relief="solid", bd=1)
        account_entry.pack(fill=tk.X, pady=(0, 15))
        account_entry.insert(0, "请输入账号标识（如：主账号、测试账号等）")
        
        # 平台选择
        tk.Label(form_frame, text="平台选择:", font=ModernStyle.NORMAL_FONT,
                fg=ModernStyle.DARK_COLOR, bg=ModernStyle.WHITE_COLOR).pack(anchor=tk.W, pady=(0, 5))
        
        self.platform_var = tk.StringVar()
        platform_combo = ttk.Combobox(form_frame, textvariable=self.platform_var,
                                     values=config.get_platform_names(),
                                     state="readonly", font=ModernStyle.NORMAL_FONT)
        platform_combo.pack(fill=tk.X, pady=(0, 15))
        platform_combo.bind('<<ComboboxSelected>>', self.update_data_path)
        
        # 数据路径
        tk.Label(form_frame, text="数据路径:", font=ModernStyle.NORMAL_FONT,
                fg=ModernStyle.DARK_COLOR, bg=ModernStyle.WHITE_COLOR).pack(anchor=tk.W, pady=(0, 5))
        
        self.data_path_var = tk.StringVar()
        data_path_entry = tk.Entry(form_frame, textvariable=self.data_path_var,
                                 font=ModernStyle.NORMAL_FONT, relief="solid", bd=1,
                                 state="readonly", bg=ModernStyle.LIGHT_COLOR)
        data_path_entry.pack(fill=tk.X, pady=(0, 15))
        
        # 说明文本
        info_frame = tk.Frame(main_frame, bg=ModernStyle.PRIMARY_COLOR, relief="solid", bd=1)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        info_text = tk.Text(info_frame, height=6, wrap=tk.WORD, font=ModernStyle.SMALL_FONT,
                          bg=ModernStyle.PRIMARY_COLOR, fg="white", relief="flat", bd=10)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        info_content = """注意：这是演示版本，当前会保存测试数据。

工作流程：
1. 填写配置信息
2. 点击确定保存配置
3. 选择是否立即进行登录授权

实际使用需要配置真实的登录流程。"""
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=ModernStyle.WHITE_COLOR)
        button_frame.pack(fill=tk.X)
        
        cancel_btn = ModernButton(button_frame, "取消", style="secondary",
                                command=self.dialog.destroy)
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        ok_btn = ModernButton(button_frame, "确定", style="primary",
                            command=self.accept_dialog)
        ok_btn.pack(side=tk.RIGHT)
    
    def update_data_path(self, event=None):
        """更新数据路径"""
        platform_name = self.platform_var.get()
        if platform_name:
            platform_key = config.get_platform_key_by_name(platform_name)
            self.data_path_var.set(platform_key)
    
    def accept_dialog(self):
        """确认对话框"""
        account_id = self.account_id_var.get().strip()
        platform_name = self.platform_var.get()
        data_path = self.data_path_var.get().strip()
        
        if not account_id or not platform_name or account_id == "请输入账号标识（如：主账号、测试账号等）":
            messagebox.showwarning("警告", "请填写完整的账号信息")
            return
        
        platform_key = config.get_platform_key_by_name(platform_name)
        
        # 先保存基础配置
        basic_config = {
            'account_id': account_id,
            'platform': platform_key,
            'data_path': data_path,
            'created_at': str(int(time.time())),
            'login_status': 'pending'
        }
        
        if cookie_manager.save_cookies(account_id, platform_key, basic_config):
            self.result_data = {
                'account_id': account_id,
                'platform': platform_key,
                'platform_name': platform_name,
                'data_path': data_path
            }
            
            # 询问是否立即登录
            reply = messagebox.askyesno("配置保存成功",
                                      f"账号配置 {account_id}@{platform_name} 已保存！\n"
                                      f"数据路径: {data_path}\n\n"
                                      "是否立即进行登录授权？")
            
            if reply:
                messagebox.showinfo("登录指引",
                                  "登录功能开发中...\n\n"
                                  "完整版本将包括：\n"
                                  "1. 自动打开浏览器\n"
                                  "2. 引导用户登录\n"
                                  "3. 自动获取登录凭证\n"
                                  "4. 保存登录状态")
            
            self.dialog.destroy()
        else:
            messagebox.showerror("错误", "保存账号配置失败")


class ModernAccountManager:
    """现代化账号管理主窗口"""

    def __init__(self):
        self.logger = get_logger('ModernAccountManager')
        self.root = tk.Tk()
        self.accounts_data = []
        self.account_cards = []
        self.setup_ui()
        self.load_accounts()

        # 定时刷新
        self.refresh_accounts()

    def setup_ui(self):
        """设置主界面"""
        self.root.title(f"{config.app_name} v{config.app_version}")
        self.root.geometry("1200x800")
        self.root.configure(bg=ModernStyle.LIGHT_COLOR)

        # 居中显示
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600)
        y = (self.root.winfo_screenheight() // 2) - (400)
        self.root.geometry(f"1200x800+{x}+{y}")

        # 创建主要组件
        self.create_header()
        self.create_toolbar()
        self.create_stats_panel()
        self.create_content_area()
        self.create_status_bar()

    def create_header(self):
        """创建标题栏"""
        header_frame = tk.Frame(self.root, bg=ModernStyle.PRIMARY_COLOR, height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # 标题容器
        title_container = tk.Frame(header_frame, bg=ModernStyle.PRIMARY_COLOR)
        title_container.pack(expand=True, fill=tk.BOTH)

        # 主标题
        title_label = tk.Label(title_container,
                             text="多平台运营数据抓取与分析系统",
                             font=("Arial", 20, "bold"),
                             fg="white",
                             bg=ModernStyle.PRIMARY_COLOR)
        title_label.pack(expand=True)

        # 副标题
        subtitle_label = tk.Label(title_container,
                                text=f"版本 {config.app_version} - 现代化管理界面",
                                font=("Arial", 12),
                                fg=ModernStyle.LIGHT_COLOR,
                                bg=ModernStyle.PRIMARY_COLOR)
        subtitle_label.pack()

    def create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = tk.Frame(self.root, bg=ModernStyle.WHITE_COLOR, height=70)
        toolbar_frame.pack(fill=tk.X, padx=20, pady=10)
        toolbar_frame.pack_propagate(False)

        # 按钮容器
        button_container = tk.Frame(toolbar_frame, bg=ModernStyle.WHITE_COLOR)
        button_container.pack(expand=True, fill=tk.BOTH)

        # 按钮
        self.add_btn = ModernButton(button_container, "➕ 添加账号",
                                  command=self.add_account, style="success")
        self.add_btn.pack(side=tk.LEFT, padx=(0, 15))

        self.refresh_btn = ModernButton(button_container, "🔄 刷新",
                                      command=self.load_accounts, style="primary")
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 15))

        self.status_btn = ModernButton(button_container, "📊 系统状态",
                                     command=self.show_system_status, style="warning")
        self.status_btn.pack(side=tk.LEFT, padx=(0, 15))

        self.test_btn = ModernButton(button_container, "🧪 运行测试",
                                   command=self.run_tests, style="secondary")
        self.test_btn.pack(side=tk.LEFT)

    def create_stats_panel(self):
        """创建统计面板"""
        stats_frame = tk.Frame(self.root, bg=ModernStyle.WHITE_COLOR, height=80)
        stats_frame.pack(fill=tk.X, padx=20, pady=(0, 10))
        stats_frame.pack_propagate(False)

        # 统计容器
        stats_container = tk.Frame(stats_frame, bg=ModernStyle.WHITE_COLOR)
        stats_container.pack(expand=True, fill=tk.BOTH, padx=20, pady=15)

        # 总账号数
        self.total_label = tk.Label(stats_container,
                                  text="总账号: 0",
                                  font=ModernStyle.HEADER_FONT,
                                  fg=ModernStyle.DARK_COLOR,
                                  bg=ModernStyle.WHITE_COLOR)
        self.total_label.pack(side=tk.LEFT)

        # 分隔符
        separator = tk.Label(stats_container, text="|", font=ModernStyle.HEADER_FONT,
                           fg=ModernStyle.SECONDARY_COLOR, bg=ModernStyle.WHITE_COLOR)
        separator.pack(side=tk.LEFT, padx=20)

        # 已登录账号数
        self.active_label = tk.Label(stats_container,
                                   text="已登录: 0",
                                   font=ModernStyle.HEADER_FONT,
                                   fg=ModernStyle.SUCCESS_COLOR,
                                   bg=ModernStyle.WHITE_COLOR)
        self.active_label.pack(side=tk.LEFT)

    def create_content_area(self):
        """创建内容区域"""
        # 滚动区域
        canvas_frame = tk.Frame(self.root, bg=ModernStyle.LIGHT_COLOR)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))

        # 创建画布和滚动条
        self.canvas = tk.Canvas(canvas_frame, bg=ModernStyle.LIGHT_COLOR, highlightthickness=0)
        scrollbar = tk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=ModernStyle.LIGHT_COLOR)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg=ModernStyle.DARK_COLOR, height=30)
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)

        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 系统已启动")

        status_label = tk.Label(status_frame,
                              textvariable=self.status_var,
                              font=ModernStyle.NORMAL_FONT,
                              fg="white",
                              bg=ModernStyle.DARK_COLOR)
        status_label.pack(side=tk.LEFT, padx=10, pady=5)

    def _on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def load_accounts(self):
        """加载账号列表"""
        try:
            self.logger.info("加载账号列表...")

            # 清空现有卡片
            self.clear_cards()

            # 获取账号数据
            accounts = cookie_manager.get_all_accounts()
            self.accounts_data = []

            active_count = 0

            for account in accounts:
                account_id = account['account_id']
                platform = account['platform']
                platform_config = config.get_platform_config(platform)
                platform_name = platform_config.get('name', platform)

                # 检查登录状态
                is_valid = cookie_manager.is_cookies_valid(account_id, platform)
                status = "✅ 已登录" if is_valid else "❌ 未登录"

                if is_valid:
                    active_count += 1

                # 从cookie数据中获取数据路径
                cookies = cookie_manager.get_cookies(account_id, platform)
                data_path = cookies.get('data_path', platform) if cookies else platform

                account_data = {
                    'account_id': account_id,
                    'platform': platform,
                    'platform_name': platform_name,
                    'status': status,
                    'data_path': data_path,
                    'updated_at': account['updated_at']
                }

                self.accounts_data.append(account_data)

            # 创建账号卡片
            self.create_account_cards()

            # 更新统计信息
            self.total_label.config(text=f"总账号: {len(accounts)}")
            self.active_label.config(text=f"已登录: {active_count}")

            # 更新状态栏
            self.status_var.set(f"已加载 {len(accounts)} 个账号，其中 {active_count} 个已登录")

            self.logger.info(f"账号列表加载完成，共{len(accounts)}个账号")

        except Exception as e:
            self.logger.error(f"加载账号列表失败: {str(e)}")
            self.status_var.set(f"加载失败: {str(e)}")

    def clear_cards(self):
        """清空账号卡片"""
        for card in self.account_cards:
            card.destroy()
        self.account_cards.clear()

    def create_account_cards(self):
        """创建账号卡片"""
        if not self.accounts_data:
            # 显示空状态
            empty_label = tk.Label(self.scrollable_frame,
                                 text="暂无账号，请点击'添加账号'按钮添加",
                                 font=ModernStyle.HEADER_FONT,
                                 fg=ModernStyle.SECONDARY_COLOR,
                                 bg=ModernStyle.LIGHT_COLOR)
            empty_label.pack(expand=True, pady=50)
            self.account_cards.append(empty_label)
            return

        # 创建网格布局
        row_frame = None
        cards_per_row = 4

        for i, account_data in enumerate(self.accounts_data):
            if i % cards_per_row == 0:
                row_frame = tk.Frame(self.scrollable_frame, bg=ModernStyle.LIGHT_COLOR)
                row_frame.pack(fill=tk.X, pady=10, padx=10)

            card = AccountCard(row_frame, account_data, self.delete_account)
            card.pack(side=tk.LEFT, padx=10)
            self.account_cards.append(card)

    def add_account(self):
        """添加账号"""
        try:
            dialog = AddAccountDialog(self.root)
            self.root.wait_window(dialog.dialog)

            if dialog.result_data:
                self.load_accounts()
                self.status_var.set(f"账号添加成功: {dialog.result_data['account_id']}")

        except Exception as e:
            self.logger.error(f"添加账号失败: {str(e)}")
            messagebox.showerror("错误", f"添加账号失败: {str(e)}")

    def delete_account(self, account_data):
        """删除账号"""
        try:
            reply = messagebox.askyesno("确认删除",
                                      f"确定要删除账号 {account_data['account_id']}@{account_data['platform_name']} 吗？")

            if reply:
                platform_key = config.get_platform_key_by_name(account_data['platform_name'])
                if cookie_manager.delete_cookies(account_data['account_id'], platform_key):
                    self.load_accounts()
                    self.status_var.set(f"账号已删除: {account_data['account_id']}")
                else:
                    messagebox.showerror("错误", "删除账号失败")

        except Exception as e:
            self.logger.error(f"删除账号失败: {str(e)}")
            messagebox.showerror("错误", f"删除账号失败: {str(e)}")

    def show_system_status(self):
        """显示系统状态"""
        try:
            from system_integration import SystemIntegration
            system = SystemIntegration()
            status = system.check_system_status()

            # 创建状态窗口
            status_window = tk.Toplevel(self.root)
            status_window.title("系统状态")
            status_window.geometry("700x500")
            status_window.configure(bg=ModernStyle.WHITE_COLOR)
            status_window.transient(self.root)
            status_window.grab_set()

            # 居中显示
            status_window.update_idletasks()
            x = (status_window.winfo_screenwidth() // 2) - (350)
            y = (status_window.winfo_screenheight() // 2) - (250)
            status_window.geometry(f"700x500+{x}+{y}")

            # 标题
            title_label = tk.Label(status_window,
                                 text="🔍 系统状态检查",
                                 font=ModernStyle.TITLE_FONT,
                                 fg=ModernStyle.DARK_COLOR,
                                 bg=ModernStyle.WHITE_COLOR)
            title_label.pack(pady=20)

            # 状态文本
            text_frame = tk.Frame(status_window, bg=ModernStyle.WHITE_COLOR)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

            text_widget = tk.Text(text_frame,
                                font=("Courier New", 11),
                                bg=ModernStyle.LIGHT_COLOR,
                                fg=ModernStyle.DARK_COLOR,
                                relief="solid",
                                bd=1)
            text_widget.pack(fill=tk.BOTH, expand=True)

            # 格式化状态信息
            status_text = self.format_system_status(status)
            text_widget.insert(tk.END, status_text)
            text_widget.config(state=tk.DISABLED)

            # 关闭按钮
            close_btn = ModernButton(status_window, "关闭", style="secondary",
                                   command=status_window.destroy)
            close_btn.pack(pady=(0, 20))

        except Exception as e:
            messagebox.showerror("错误", f"检查系统状态失败: {str(e)}")

    def format_system_status(self, status):
        """格式化系统状态信息"""
        lines = []
        lines.append("系统状态检查结果")
        lines.append("=" * 50)
        lines.append("")

        for component, info in status.items():
            status_icon = "✅" if info['status'] == 'ok' else "❌" if info['status'] == 'error' else "⚠️"
            lines.append(f"{status_icon} {component.upper()}: {info['status']}")

            if info['status'] == 'error':
                lines.append(f"   错误: {info.get('error', '未知错误')}")
            elif component == 'accounts' and 'total_accounts' in info:
                lines.append(f"   总账号数: {info['total_accounts']}")
                lines.append(f"   有效账号数: {info['valid_accounts']}")

            lines.append("")

        return "\n".join(lines)

    def run_tests(self):
        """运行测试"""
        try:
            self.status_var.set("正在运行测试...")
            self.test_btn.config(state="disabled")

            def run_tests_thread():
                try:
                    from run_tests import main as run_tests_main
                    result = run_tests_main()

                    # 在主线程中显示结果
                    self.root.after(0, lambda: self.show_test_result(result))

                except Exception as e:
                    self.root.after(0, lambda: self.show_test_result(-1, str(e)))

            threading.Thread(target=run_tests_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"启动测试失败: {str(e)}")
            messagebox.showerror("错误", f"启动测试失败: {str(e)}")
            self.test_btn.config(state="normal")

    def show_test_result(self, result, error=None):
        """显示测试结果"""
        self.test_btn.config(state="normal")

        if error:
            messagebox.showerror("测试失败", f"运行测试失败: {error}")
            self.status_var.set("测试失败")
        elif result == 0:
            messagebox.showinfo("测试完成",
                              "🎉 所有测试通过！\n\n测试报告已保存到 test_report.txt")
            self.status_var.set("测试完成 - 全部通过")
        else:
            messagebox.showwarning("测试完成",
                                 "⚠️ 部分测试失败\n\n请查看 test_report.txt 了解详情")
            self.status_var.set("测试完成 - 有失败")

    def refresh_accounts(self):
        """定时刷新账号状态"""
        self.load_accounts()
        # 30秒后再次刷新
        self.root.after(30000, self.refresh_accounts)

    def run(self):
        """运行应用"""
        try:
            print("🚀 启动现代化tkinter界面...")
            self.root.mainloop()
            print("✅ 应用程序结束")
        except Exception as e:
            print(f"❌ 应用运行异常: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    try:
        print(f"🚀 启动 {config.app_name} v{config.app_version}")
        print("=" * 60)
        print("✨ 使用现代化tkinter界面")

        # 检查Python版本
        if sys.version_info < (3, 9):
            print("❌ 错误: 需要Python 3.9或更高版本")
            return 1

        print("✅ Python版本检查通过")
        print("✅ tkinter依赖已安装")

        # 创建并运行应用
        app = ModernAccountManager()
        app.run()

        return 0

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
