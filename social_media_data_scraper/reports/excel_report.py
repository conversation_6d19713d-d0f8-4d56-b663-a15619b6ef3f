"""
Excel报告生成器
生成格式化的Excel分析报告
"""

import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.chart import <PERSON><PERSON><PERSON>, LineChart, Reference

from app.config import config
from app.logger import get_logger
from utils.file_utils import file_utils
from data.data_analyzer import data_analyzer


class ExcelReportGenerator:
    """Excel报告生成器"""
    
    def __init__(self):
        self.logger = get_logger('ExcelReportGenerator')
        self.workbook = None
        self.report_data = []
        
        # 样式定义
        self.header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        self.header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.data_font = Font(name='Arial', size=10)
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        self.center_alignment = Alignment(horizontal='center', vertical='center')
    
    def add_data(self, data: Dict[str, Any]):
        """添加数据到报告"""
        try:
            if data and 'data' in data:
                self.report_data.append(data)
                self.logger.info(f"添加数据到报告: {data.get('platform', 'unknown')}@{data.get('account_id', 'unknown')}")
            
        except Exception as e:
            self.logger.error(f"添加数据失败: {str(e)}")
    
    def generate_report(self, filename: str = None) -> Optional[Path]:
        """生成Excel报告"""
        try:
            if not self.report_data:
                self.logger.warning("没有数据可生成报告")
                return None
            
            # 生成文件名
            if not filename:
                filename = file_utils.generate_report_filename()
            
            report_path = config.report_dir / filename
            
            # 创建工作簿
            self.workbook = Workbook()
            
            # 删除默认工作表
            self.workbook.remove(self.workbook.active)
            
            # 生成各个工作表
            self._create_summary_sheet()
            self._create_detailed_data_sheet()
            self._create_platform_comparison_sheet()
            self._create_trend_analysis_sheet()
            
            # 保存文件
            file_utils.ensure_directory(report_path.parent)
            self.workbook.save(report_path)
            
            self.logger.info(f"Excel报告生成成功: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"生成Excel报告失败: {str(e)}")
            return None
    
    def _create_summary_sheet(self):
        """创建汇总工作表"""
        try:
            ws = self.workbook.create_sheet("数据汇总", 0)
            
            # 标题
            ws['A1'] = f"{config.app_name} - 数据分析报告"
            ws['A1'].font = Font(name='Arial', size=16, bold=True)
            ws.merge_cells('A1:F1')
            
            # 生成时间
            ws['A2'] = f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws['A2'].font = Font(name='Arial', size=10, color='666666')
            ws.merge_cells('A2:F2')
            
            # 基础统计
            summary_data = data_analyzer.generate_summary_report(self.report_data)
            
            row = 4
            ws[f'A{row}'] = "基础统计"
            ws[f'A{row}'].font = self.header_font
            ws[f'A{row}'].fill = self.header_fill
            ws.merge_cells(f'A{row}:B{row}')
            
            row += 1
            ws[f'A{row}'] = "总记录数"
            ws[f'B{row}'] = summary_data.get('total_records', 0)
            
            row += 1
            ws[f'A{row}'] = "涉及平台"
            ws[f'B{row}'] = ', '.join(summary_data.get('platforms', []))
            
            row += 1
            ws[f'A{row}'] = "分析周期"
            analysis_period = summary_data.get('analysis_period', {})
            period_text = f"{analysis_period.get('start', '')} 至 {analysis_period.get('end', '')}"
            ws[f'B{row}'] = period_text
            
            # 平台汇总
            row += 2
            ws[f'A{row}'] = "平台汇总"
            ws[f'A{row}'].font = self.header_font
            ws[f'A{row}'].fill = self.header_fill
            ws.merge_cells(f'A{row}:F{row}')
            
            row += 1
            headers = ['平台', '账号数量', '总收入', '退款金额', '订单数量', '转化率']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.font = self.header_font
                cell.fill = self.header_fill
                cell.border = self.border
                cell.alignment = self.center_alignment
            
            # 填充平台数据
            platform_summary = summary_data.get('platform_summary', {})
            for platform, data in platform_summary.items():
                row += 1
                platform_config = config.get_platform_config(platform)
                platform_name = platform_config.get('name', platform)
                
                platform_data = data.get('data', {})
                values = [
                    platform_name,
                    data.get('count', 0),
                    platform_data.get('total_revenue', 0),
                    platform_data.get('successful_refunds', 0),
                    platform_data.get('order_count', 0),
                    f"{platform_data.get('conversion_rate', 0):.2f}%"
                ]
                
                for col, value in enumerate(values, 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.font = self.data_font
                    cell.border = self.border
                    cell.alignment = self.center_alignment
            
            # 调整列宽
            for col in range(1, 7):
                ws.column_dimensions[chr(64 + col)].width = 15
            
        except Exception as e:
            self.logger.error(f"创建汇总工作表失败: {str(e)}")
    
    def _create_detailed_data_sheet(self):
        """创建详细数据工作表"""
        try:
            ws = self.workbook.create_sheet("详细数据")
            
            # 准备数据
            detailed_data = []
            for item in self.report_data:
                row_data = {
                    '平台': config.get_platform_config(item.get('platform', '')).get('name', ''),
                    '账号ID': item.get('account_id', ''),
                    '抓取时间': item.get('timestamp', ''),
                }
                
                # 添加具体数据字段
                item_data = item.get('data', {})
                for key, value in item_data.items():
                    if isinstance(value, (int, float)):
                        row_data[key] = value
                    elif isinstance(value, str) and value.strip():
                        row_data[key] = value
                
                detailed_data.append(row_data)
            
            if not detailed_data:
                ws['A1'] = "暂无详细数据"
                return
            
            # 创建DataFrame
            df = pd.DataFrame(detailed_data)
            
            # 写入表头
            for col, header in enumerate(df.columns, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = self.header_font
                cell.fill = self.header_fill
                cell.border = self.border
                cell.alignment = self.center_alignment
            
            # 写入数据
            for row_idx, row_data in enumerate(df.values, 2):
                for col_idx, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.font = self.data_font
                    cell.border = self.border
                    
                    # 数值居中对齐
                    if isinstance(value, (int, float)):
                        cell.alignment = self.center_alignment
            
            # 调整列宽
            for col in range(1, len(df.columns) + 1):
                ws.column_dimensions[chr(64 + col)].width = 12
            
        except Exception as e:
            self.logger.error(f"创建详细数据工作表失败: {str(e)}")
    
    def _create_platform_comparison_sheet(self):
        """创建平台对比工作表"""
        try:
            ws = self.workbook.create_sheet("平台对比")
            
            # 按平台分组数据
            platform_data = {}
            for item in self.report_data:
                platform = item.get('platform', '')
                if platform not in platform_data:
                    platform_data[platform] = []
                platform_data[platform].append(item)
            
            if not platform_data:
                ws['A1'] = "暂无平台数据"
                return
            
            # 创建对比表
            row = 1
            ws[f'A{row}'] = "平台对比分析"
            ws[f'A{row}'].font = Font(name='Arial', size=14, bold=True)
            ws.merge_cells(f'A{row}:E{row}')
            
            row += 2
            headers = ['平台', '账号数', '平均收入', '平均订单数', '平均转化率']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.font = self.header_font
                cell.fill = self.header_fill
                cell.border = self.border
                cell.alignment = self.center_alignment
            
            # 计算平台统计数据
            for platform, items in platform_data.items():
                row += 1
                platform_config = config.get_platform_config(platform)
                platform_name = platform_config.get('name', platform)
                
                # 计算平均值
                total_revenue = sum(item.get('data', {}).get('total_revenue', 0) for item in items)
                total_orders = sum(item.get('data', {}).get('order_count', 0) for item in items)
                total_conversion = sum(item.get('data', {}).get('conversion_rate', 0) for item in items)
                
                account_count = len(items)
                avg_revenue = total_revenue / account_count if account_count > 0 else 0
                avg_orders = total_orders / account_count if account_count > 0 else 0
                avg_conversion = total_conversion / account_count if account_count > 0 else 0
                
                values = [
                    platform_name,
                    account_count,
                    f"{avg_revenue:.2f}",
                    f"{avg_orders:.0f}",
                    f"{avg_conversion:.2f}%"
                ]
                
                for col, value in enumerate(values, 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.font = self.data_font
                    cell.border = self.border
                    cell.alignment = self.center_alignment
            
            # 调整列宽
            for col in range(1, 6):
                ws.column_dimensions[chr(64 + col)].width = 15
            
        except Exception as e:
            self.logger.error(f"创建平台对比工作表失败: {str(e)}")
    
    def _create_trend_analysis_sheet(self):
        """创建趋势分析工作表"""
        try:
            ws = self.workbook.create_sheet("趋势分析")
            
            # 按时间排序数据
            sorted_data = sorted(self.report_data, key=lambda x: x.get('timestamp', ''))
            
            if len(sorted_data) < 2:
                ws['A1'] = "数据不足，无法进行趋势分析（需要至少2个时间点的数据）"
                return
            
            # 分析收入趋势
            revenue_trend = data_analyzer.analyze_trends(sorted_data, 'total_revenue')
            
            row = 1
            ws[f'A{row}'] = "趋势分析报告"
            ws[f'A{row}'].font = Font(name='Arial', size=14, bold=True)
            ws.merge_cells(f'A{row}:B{row}')
            
            if revenue_trend:
                row += 2
                ws[f'A{row}'] = "收入趋势分析"
                ws[f'A{row}'].font = self.header_font
                ws[f'A{row}'].fill = self.header_fill
                ws.merge_cells(f'A{row}:B{row}')
                
                trend_items = [
                    ('数据点数量', revenue_trend.get('data_points', 0)),
                    ('起始值', f"{revenue_trend.get('start_value', 0):.2f}"),
                    ('结束值', f"{revenue_trend.get('end_value', 0):.2f}"),
                    ('最大值', f"{revenue_trend.get('max_value', 0):.2f}"),
                    ('最小值', f"{revenue_trend.get('min_value', 0):.2f}"),
                    ('平均值', f"{revenue_trend.get('average_value', 0):.2f}"),
                    ('总变化', f"{revenue_trend.get('total_change', 0):.2f}"),
                    ('趋势方向', revenue_trend.get('trend_direction', '未知')),
                    ('变化率', f"{revenue_trend.get('change_rate', 0):.2f}%")
                ]
                
                for label, value in trend_items:
                    row += 1
                    ws[f'A{row}'] = label
                    ws[f'B{row}'] = value
                    ws[f'A{row}'].font = self.data_font
                    ws[f'B{row}'].font = self.data_font
            
            # 调整列宽
            ws.column_dimensions['A'].width = 20
            ws.column_dimensions['B'].width = 15
            
        except Exception as e:
            self.logger.error(f"创建趋势分析工作表失败: {str(e)}")


# 全局报告生成器实例
excel_report_generator = ExcelReportGenerator()
