#!/usr/bin/env python3
"""
启动脚本
提供多种启动选项
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.logger import get_logger


def show_banner():
    """显示启动横幅"""
    print("=" * 60)
    print(f"  {config.app_name}")
    print(f"  版本: {config.app_version}")
    print("=" * 60)
    print()


def show_menu():
    """显示菜单"""
    print("启动GUI应用程序...")
    print()


def start_gui():
    """启动GUI应用程序"""
    try:
        print("启动现代化GUI应用程序...")
        from start_modern import main
        return main()
    except ImportError as e:
        print(f"启动GUI失败，缺少依赖: {e}")
        print("请运行: pip install PyQt5")
        return 1
    except Exception as e:
        print(f"启动GUI失败: {e}")
        return 1


def run_integration_test():
    """运行系统集成测试"""
    try:
        print("运行系统集成测试...")
        from system_integration import main
        return main()
    except Exception as e:
        print(f"系统集成测试失败: {e}")
        return 1


def run_unit_tests():
    """运行单元测试"""
    try:
        print("运行单元测试...")
        from run_tests import main
        return main()
    except Exception as e:
        print(f"单元测试失败: {e}")
        return 1


def show_system_status():
    """显示系统状态"""
    try:
        from system_integration import SystemIntegration
        
        system = SystemIntegration()
        status = system.check_system_status()
        
        print("系统状态检查结果:")
        print("-" * 40)
        
        for component, info in status.items():
            status_icon = "✅" if info['status'] == 'ok' else "❌" if info['status'] == 'error' else "⚠️"
            print(f"{status_icon} {component.upper()}: {info['status']}")
            
            if info['status'] == 'error':
                print(f"   错误: {info.get('error', '未知错误')}")
            elif component == 'accounts' and 'total_accounts' in info:
                print(f"   总账号数: {info['total_accounts']}")
                print(f"   有效账号数: {info['valid_accounts']}")
        
        print()
        return 0
        
    except Exception as e:
        print(f"检查系统状态失败: {e}")
        return 1


def main():
    """主函数"""
    logger = get_logger('Starter')
    
    show_banner()
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print("❌ 错误: 需要Python 3.9或更高版本")
        print(f"当前版本: {sys.version}")
        return 1
    
    # 检查依赖文件
    requirements_file = project_root / 'requirements.txt'
    if not requirements_file.exists():
        print("❌ 错误: 找不到requirements.txt文件")
        return 1
    
    try:
        show_menu()
        return start_gui()

    except KeyboardInterrupt:
        print("\n\n用户取消操作")
        return 0
    except Exception as e:
        logger.error(f"启动脚本异常: {str(e)}")
        print(f"发生错误: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
