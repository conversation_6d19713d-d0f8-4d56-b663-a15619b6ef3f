"""
配置管理模块
负责加载和管理应用程序的配置信息
"""

import os
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv


class Config:
    """配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        # 获取项目根目录
        self.root_dir = Path(__file__).parent.parent
        
        # 加载环境变量
        env_path = self.root_dir / '.env'
        load_dotenv(env_path)
        
        # 应用配置
        self.app_name = os.getenv('APP_NAME', '多平台运营数据抓取与分析系统')
        self.app_version = os.getenv('APP_VERSION', '1.0.0')
        self.debug = os.getenv('DEBUG', 'False').lower() == 'true'
        
        # 目录配置
        self.data_dir = self._get_path('DATA_DIR', 'data')
        self.report_dir = self._get_path('REPORT_DIR', 'report')
        self.log_dir = self._get_path('LOG_DIR', 'logs')
        
        # 确保目录存在
        self._ensure_directories()
        
        # 数据库配置
        self.cookie_file = self._get_path('COOKIE_FILE', 'data/cookies.db')
        
        # 日志配置
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        self.log_file = self._get_path('LOG_FILE', 'logs/app.log')
        
        # 抓取配置
        self.request_timeout = int(os.getenv('REQUEST_TIMEOUT', '30'))
        self.retry_times = int(os.getenv('RETRY_TIMES', '3'))
        self.retry_delay = int(os.getenv('RETRY_DELAY', '5'))
        
        # 安全配置
        self.encryption_key = os.getenv('ENCRYPTION_KEY', 'default-secret-key')
        
        # 平台配置
        self.platforms = {
            'xiaohongshu': {
                'name': '小红书',
                'base_url': os.getenv('XIAOHONGSHU_BASE_URL', 'https://www.xiaohongshu.com'),
                'icon': 'xiaohongshu.png'
            },
            'douyin': {
                'name': '抖音',
                'base_url': os.getenv('DOUYIN_BASE_URL', 'https://www.douyin.com'),
                'icon': 'douyin.png'
            },
            'kuaishou': {
                'name': '快手',
                'base_url': os.getenv('KUAISHOU_BASE_URL', 'https://www.kuaishou.com'),
                'icon': 'kuaishou.png'
            }
        }
    
    def _get_path(self, env_key: str, default_path: str) -> Path:
        """获取路径配置"""
        path_str = os.getenv(env_key, default_path)
        if os.path.isabs(path_str):
            return Path(path_str)
        return self.root_dir / path_str
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [self.data_dir, self.report_dir, self.log_dir]
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_platform_config(self, platform_key: str) -> Dict[str, Any]:
        """获取平台配置"""
        return self.platforms.get(platform_key, {})
    
    def get_platform_names(self) -> list:
        """获取所有平台名称"""
        return [config['name'] for config in self.platforms.values()]
    
    def get_platform_key_by_name(self, platform_name: str) -> str:
        """根据平台名称获取平台键"""
        for key, config in self.platforms.items():
            if config['name'] == platform_name:
                return key
        return ''


# 全局配置实例
config = Config()
