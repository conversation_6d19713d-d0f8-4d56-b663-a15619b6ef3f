"""
日志系统模块
提供统一的日志记录功能
"""

import sys
from pathlib import Path
from loguru import logger
from .config import config


class Logger:
    """日志管理类"""
    
    def __init__(self):
        """初始化日志系统"""
        self._setup_logger()
    
    def _setup_logger(self):
        """配置日志系统"""
        # 移除默认的控制台输出
        logger.remove()
        
        # 添加控制台输出
        logger.add(
            sys.stdout,
            level=config.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # 添加文件输出
        logger.add(
            config.log_file,
            level=config.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 添加错误日志文件
        error_log_file = config.log_dir / "error.log"
        logger.add(
            error_log_file,
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
    
    def get_logger(self, name: str = None):
        """获取日志记录器"""
        if name:
            return logger.bind(name=name)
        return logger


# 全局日志实例
app_logger = Logger()

# 便捷的日志函数
def get_logger(name: str = None):
    """获取日志记录器"""
    return app_logger.get_logger(name)

def log_info(message: str, name: str = None):
    """记录信息日志"""
    get_logger(name).info(message)

def log_warning(message: str, name: str = None):
    """记录警告日志"""
    get_logger(name).warning(message)

def log_error(message: str, name: str = None):
    """记录错误日志"""
    get_logger(name).error(message)

def log_debug(message: str, name: str = None):
    """记录调试日志"""
    get_logger(name).debug(message)
