"""
主入口文件
启动应用程序
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

from app.config import config
from app.logger import get_logger
from gui.account_manager import AccountManagerWindow


class Application:
    """主应用程序类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.logger = get_logger('Application')
        self.app = None
        self.main_window = None
    
    def setup_app(self):
        """设置应用程序"""
        # 创建QApplication实例
        self.app = QApplication(sys.argv)
        
        # 设置应用程序属性
        self.app.setApplicationName(config.app_name)
        self.app.setApplicationVersion(config.app_version)
        self.app.setOrganizationName("DataScraper")
        
        # 设置高DPI支持
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 设置应用程序图标
        icon_path = project_root / 'gui' / 'resources' / 'app_icon.png'
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        
        self.logger.info(f"应用程序初始化完成: {config.app_name} v{config.app_version}")
    
    def create_main_window(self):
        """创建主窗口"""
        self.main_window = AccountManagerWindow()
        self.main_window.show()
        self.logger.info("主窗口创建完成")
    
    def run(self):
        """运行应用程序"""
        try:
            self.logger.info("启动应用程序...")
            self.setup_app()
            self.create_main_window()
            
            # 运行事件循环
            exit_code = self.app.exec_()
            self.logger.info(f"应用程序退出，退出码: {exit_code}")
            return exit_code
            
        except Exception as e:
            self.logger.error(f"应用程序运行出错: {str(e)}")
            return 1


def main():
    """主函数"""
    app = Application()
    return app.run()


if __name__ == '__main__':
    sys.exit(main())
